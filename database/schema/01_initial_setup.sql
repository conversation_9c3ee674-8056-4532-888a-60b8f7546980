-- 01_initial_setup.sql
-- Initial database setup for Universal Web Directory

-- Database creation and user setup (run as postgres superuser)
-- CREATE DATABASE web_crawler;
-- CREATE USER webc WITH PASSWORD 'j9xuvUyTHBKEld4JeP9FO';
-- GRANT ALL PRIVILEGES ON DATABASE web_crawler TO webc;

-- Connect to the web_crawler database before running the rest

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";  -- For fuzzy text search
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- For composite indexes
-- CREATE EXTENSION IF NOT EXISTS "pgvector";  -- For vector embeddings (install separately)

-- Performance tuning (adjust based on your system)
-- ALTER SYSTEM SET shared_buffers = '256MB';
-- ALTER SYSTEM SET effective_cache_size = '1GB';
-- ALTER SYSTEM SET maintenance_work_mem = '64MB';
-- ALTER SYSTEM SET checkpoint_completion_target = 0.9;
-- ALTER SYSTEM SET wal_buffers = '16MB';
-- SELECT pg_reload_conf();

-- Create core tables for Phase 0

-- Sites table - represents domains/websites
CREATE TABLE IF NOT EXISTS sites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) NOT NULL UNIQUE,
    title TEXT,
    description TEXT,
    language VARCHAR(10) DEFAULT 'en',
    robots_txt TEXT,
    robots_last_fetched TIMESTAMP,
    crawl_delay INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_crawled TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- URL frontier - queue of URLs to crawl
CREATE TABLE IF NOT EXISTS url_frontier (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    priority INTEGER DEFAULT 5,
    depth INTEGER DEFAULT 0,
    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scheduled_for TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    status VARCHAR(20) DEFAULT 'pending',
    error_message TEXT,
    source_url TEXT,
    source_page_id UUID
);

-- Pages table - crawled page content
CREATE TABLE IF NOT EXISTS pages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    title TEXT,
    content_hash VARCHAR(64),
    html_content TEXT,
    text_content TEXT,
    word_count INTEGER,
    language VARCHAR(10),
    http_status INTEGER,
    content_type VARCHAR(100),
    content_length INTEGER,
    crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP,
    etag VARCHAR(255),
    is_duplicate BOOLEAN DEFAULT false,
    duplicate_of UUID REFERENCES pages(id)
);

-- Links table - relationships between pages
CREATE TABLE IF NOT EXISTS links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_page_id UUID REFERENCES pages(id) ON DELETE CASCADE,
    to_url TEXT NOT NULL,
    to_page_id UUID REFERENCES pages(id) ON DELETE SET NULL,
    anchor_text TEXT,
    link_type VARCHAR(20) DEFAULT 'internal',
    position INTEGER,
    is_nofollow BOOLEAN DEFAULT false,
    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Crawl logs for monitoring and debugging
CREATE TABLE IF NOT EXISTS crawl_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL,
    http_status INTEGER,
    response_time_ms INTEGER,
    content_length INTEGER,
    error_message TEXT,
    user_agent TEXT,
    crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_sites_domain ON sites(domain);
CREATE INDEX IF NOT EXISTS idx_sites_active ON sites(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_url_frontier_status ON url_frontier(status);
CREATE INDEX IF NOT EXISTS idx_url_frontier_scheduled ON url_frontier(scheduled_for) WHERE status = 'pending';
CREATE INDEX IF NOT EXISTS idx_url_frontier_site ON url_frontier(site_id);
CREATE INDEX IF NOT EXISTS idx_url_frontier_priority ON url_frontier(priority DESC, scheduled_for);

CREATE INDEX IF NOT EXISTS idx_pages_site ON pages(site_id);
CREATE INDEX IF NOT EXISTS idx_pages_url ON pages(url);
CREATE INDEX IF NOT EXISTS idx_pages_hash ON pages(content_hash);
CREATE INDEX IF NOT EXISTS idx_pages_crawled ON pages(crawled_at);
CREATE INDEX IF NOT EXISTS idx_pages_status ON pages(http_status);

CREATE INDEX IF NOT EXISTS idx_links_from_page ON links(from_page_id);
CREATE INDEX IF NOT EXISTS idx_links_to_page ON links(to_page_id);
CREATE INDEX IF NOT EXISTS idx_links_to_url ON links(to_url);

CREATE INDEX IF NOT EXISTS idx_crawl_logs_site ON crawl_logs(site_id);
CREATE INDEX IF NOT EXISTS idx_crawl_logs_status ON crawl_logs(status);
CREATE INDEX IF NOT EXISTS idx_crawl_logs_crawled ON crawl_logs(crawled_at);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sites_updated_at BEFORE UPDATE ON sites
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial test data
INSERT INTO sites (domain, title, description) VALUES 
('example.com', 'Example Domain', 'Example domain for testing')
ON CONFLICT (domain) DO NOTHING;

-- Grant permissions to application user
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO webc;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO webc;
