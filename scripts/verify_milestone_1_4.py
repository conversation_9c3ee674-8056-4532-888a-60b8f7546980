#!/usr/bin/env python3
"""
Milestone 1.4: Comprehensive Database Architecture Verification Script

This script verifies that the comprehensive database schema for the Universal Web Directory
has been fully implemented with all required fields, indexes, and relationships.
"""

import sys
import os
import logging
from datetime import datetime
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import DatabaseConfig
from models import Site, Page

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_schema():
    """Test 1: Verify comprehensive database schema exists"""
    print("🔍 Test 1: Database Schema Verification")
    
    config = DatabaseConfig()
    engine = create_engine(config.connection_string)
    inspector = inspect(engine)
    
    # Check sites table structure
    sites_columns = inspector.get_columns('sites')
    sites_column_names = [col['name'] for col in sites_columns]
    
    required_sites_fields = [
        'id', 'domain', 'subdomain', 'title', 'description', 'language', 'country',
        'ip_address', 'server_software', 'cms_platform', 'technology_stack',
        'robots_txt_url', 'sitemap_urls', 'favicon_url',
        'avg_response_time', 'uptime_percentage', 'ssl_enabled', 'mobile_friendly',
        'estimated_page_count', 'content_freshness_score', 'update_frequency',
        'domain_age_days', 'backlink_count', 'authority_score', 'spam_score',
        'first_discovered', 'last_crawled', 'next_crawl_scheduled', 'crawl_frequency_hours', 'crawl_priority',
        'status', 'error_count', 'last_error', 'created_at', 'updated_at'
    ]
    
    missing_sites_fields = [field for field in required_sites_fields if field not in sites_column_names]
    if missing_sites_fields:
        print(f"  ❌ Missing sites fields: {missing_sites_fields}")
        return False
    else:
        print(f"  ✅ Sites table has all {len(required_sites_fields)} required fields")
    
    # Check pages table structure
    pages_columns = inspector.get_columns('pages')
    pages_column_names = [col['name'] for col in pages_columns]
    
    required_pages_fields = [
        'id', 'site_id', 'url', 'url_hash', 'title', 'meta_description', 'meta_keywords', 'canonical_url',
        'content_text', 'content_html', 'content_length', 'word_count',
        'heading_h1', 'heading_h2', 'heading_h3', 'internal_links_count', 'external_links_count', 'image_count',
        'http_status', 'content_type', 'charset', 'response_time_ms',
        'page_rank_score', 'readability_score', 'keyword_density',
        'social_shares_count', 'comments_count',
        'content_type_classification', 'topic_categories', 'sentiment_score',
        'first_discovered', 'last_crawled', 'last_modified', 'etag',
        'content_hash', 'previous_content_hash', 'change_frequency',
        'created_at', 'updated_at'
    ]
    
    missing_pages_fields = [field for field in required_pages_fields if field not in pages_column_names]
    if missing_pages_fields:
        print(f"  ❌ Missing pages fields: {missing_pages_fields}")
        return False
    else:
        print(f"  ✅ Pages table has all {len(required_pages_fields)} required fields")
    
    # Check indexes
    sites_indexes = inspector.get_indexes('sites')
    pages_indexes = inspector.get_indexes('pages')
    
    required_sites_indexes = ['idx_sites_domain', 'idx_sites_last_crawled', 'idx_sites_next_crawl', 'idx_sites_status', 'idx_sites_authority_score']
    required_pages_indexes = ['idx_pages_site_id', 'idx_pages_url_hash', 'idx_pages_last_crawled', 'idx_pages_content_type', 'idx_pages_topic_categories', 'idx_pages_http_status']
    
    sites_index_names = [idx['name'] for idx in sites_indexes]
    pages_index_names = [idx['name'] for idx in pages_indexes]
    
    missing_sites_indexes = [idx for idx in required_sites_indexes if idx not in sites_index_names]
    missing_pages_indexes = [idx for idx in required_pages_indexes if idx not in pages_index_names]
    
    if missing_sites_indexes or missing_pages_indexes:
        print(f"  ❌ Missing indexes - Sites: {missing_sites_indexes}, Pages: {missing_pages_indexes}")
        return False
    else:
        print(f"  ✅ All required indexes present")
    
    return True

def test_sqlalchemy_models():
    """Test 2: Verify SQLAlchemy models match schema"""
    print("🔍 Test 2: SQLAlchemy Models Verification")
    
    try:
        config = DatabaseConfig()
        engine = create_engine(config.connection_string)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Test Site model
        site_columns = [column.name for column in Site.__table__.columns]
        print(f"  ✅ Site model has {len(site_columns)} columns")
        
        # Test Page model
        page_columns = [column.name for column in Page.__table__.columns]
        print(f"  ✅ Page model has {len(page_columns)} columns")
        
        # Test relationships
        site_relationships = [rel.key for rel in Site.__mapper__.relationships]
        page_relationships = [rel.key for rel in Page.__mapper__.relationships]
        
        print(f"  ✅ Site relationships: {site_relationships}")
        print(f"  ✅ Page relationships: {page_relationships}")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"  ❌ SQLAlchemy model error: {e}")
        return False

def test_data_migration():
    """Test 3: Verify data migration was successful"""
    print("🔍 Test 3: Data Migration Verification")
    
    try:
        config = DatabaseConfig()
        engine = create_engine(config.connection_string)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Check data counts
        sites_count = session.query(Site).count()
        pages_count = session.query(Page).count()
        
        print(f"  ✅ Sites migrated: {sites_count}")
        print(f"  ✅ Pages migrated: {pages_count}")
        
        # Check sample data integrity
        if sites_count > 0:
            sample_site = session.query(Site).first()
            print(f"  ✅ Sample site: {sample_site.domain} (status: {sample_site.status})")
        
        if pages_count > 0:
            sample_page = session.query(Page).first()
            print(f"  ✅ Sample page: {sample_page.url[:50]}... (status: {sample_page.http_status})")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Data migration verification error: {e}")
        return False

def test_advanced_features():
    """Test 4: Verify advanced database features"""
    print("🔍 Test 4: Advanced Features Verification")
    
    try:
        config = DatabaseConfig()
        engine = create_engine(config.connection_string)
        
        # Test JSONB functionality
        with engine.connect() as conn:
            result = conn.execute(text("SELECT technology_stack FROM sites WHERE technology_stack IS NOT NULL LIMIT 1"))
            if result.rowcount > 0:
                print("  ✅ JSONB technology_stack field working")
            
            # Test array functionality
            result = conn.execute(text("SELECT sitemap_urls FROM sites WHERE sitemap_urls IS NOT NULL LIMIT 1"))
            if result.rowcount > 0:
                print("  ✅ Array sitemap_urls field working")
            
            # Test INET functionality
            result = conn.execute(text("SELECT ip_address FROM sites WHERE ip_address IS NOT NULL LIMIT 1"))
            if result.rowcount > 0:
                print("  ✅ INET ip_address field working")
            
            # Test GIN index on topic_categories
            result = conn.execute(text("EXPLAIN SELECT * FROM pages WHERE topic_categories && ARRAY['technology']"))
            explain_text = str(result.fetchall())
            if 'idx_pages_topic_categories' in explain_text:
                print("  ✅ GIN index on topic_categories working")
            else:
                print("  ⚠️  GIN index on topic_categories not being used (may be due to empty data)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Advanced features error: {e}")
        return False

def test_comprehensive_crud():
    """Test 5: Comprehensive CRUD operations"""
    print("🔍 Test 5: Comprehensive CRUD Operations")
    
    try:
        config = DatabaseConfig()
        engine = create_engine(config.connection_string)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Create test site with comprehensive data
        test_site = Site(
            domain='test-comprehensive.example',
            title='Test Comprehensive Site',
            description='A test site for comprehensive schema verification',
            language='en',
            country='US',
            technology_stack={'framework': 'Django', 'database': 'PostgreSQL'},
            sitemap_urls=['http://test-comprehensive.example/sitemap.xml'],
            ssl_enabled=True,
            authority_score=0.75,
            spam_score=0.1,
            crawl_priority=8,
            status='active'
        )
        session.add(test_site)
        session.commit()
        
        # Create test page with comprehensive data
        test_page = Page(
            site_id=test_site.id,
            url='http://test-comprehensive.example/test-page',
            url_hash='test_hash_comprehensive_1234567890abcdef',
            title='Test Comprehensive Page',
            meta_description='A comprehensive test page',
            meta_keywords=['test', 'comprehensive', 'schema'],
            content_text='This is comprehensive test content.',
            content_html='<p>This is comprehensive test content.</p>',
            content_length=1000,
            word_count=150,
            heading_h1=['Main Heading'],
            heading_h2=['Sub Heading 1', 'Sub Heading 2'],
            internal_links_count=5,
            external_links_count=2,
            image_count=3,
            http_status=200,
            content_type='text/html',
            charset='utf-8',
            response_time_ms=250,
            page_rank_score=0.6,
            readability_score=0.8,
            keyword_density={'test': 0.05, 'comprehensive': 0.03},
            social_shares_count=10,
            comments_count=2,
            content_type_classification='article',
            topic_categories=['technology', 'testing'],
            sentiment_score=0.2,
            content_hash='abcdef1234567890'
        )
        session.add(test_page)
        session.commit()
        
        print(f"  ✅ Created comprehensive test site: {test_site.domain}")
        print(f"  ✅ Created comprehensive test page: {test_page.url}")
        
        # Test complex queries
        sites_with_high_authority = session.query(Site).filter(Site.authority_score > 0.5).count()
        pages_with_categories = session.query(Page).filter(Page.topic_categories.isnot(None)).count()
        
        print(f"  ✅ Complex query - High authority sites: {sites_with_high_authority}")
        print(f"  ✅ Complex query - Pages with categories: {pages_with_categories}")
        
        # Cleanup test data
        session.delete(test_page)
        session.delete(test_site)
        session.commit()
        session.close()
        
        return True
        
    except Exception as e:
        print(f"  ❌ CRUD operations error: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🚀 Starting Milestone 1.4: Comprehensive Database Architecture Verification")
    print("=" * 80)
    
    tests = [
        test_database_schema,
        test_sqlalchemy_models,
        test_data_migration,
        test_advanced_features,
        test_comprehensive_crud
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test in tests:
        try:
            if test():
                passed_tests += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
            print()
    
    print("=" * 80)
    print(f"📊 VERIFICATION RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ✅ Milestone 1.4: Comprehensive Database Architecture FULLY IMPLEMENTED!")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
