import os
import sys
from urllib.parse import urlparse
from sqlalchemy.orm import Session
# Assuming models.url_frontier and config.database will be available
# as per other milestones or will be stubbed in the next step.
from models.url_frontier import <PERSON><PERSON><PERSON><PERSON><PERSON>
from config.database import <PERSON><PERSON><PERSON>al
from datetime import datetime

class SeedDataLoader:
    def __init__(self, seed_file_path: str):
        self.seed_file_path = seed_file_path
        self.session = SessionLocal()

    def load_directory_websites(self):
        """Load directory websites as high-priority seed URLs"""
        # Ensure the seed file path is correct relative to the project root
        # If the script is in 'scripts/' and the seed file is in 'directory_link_source/',
        # the path needs to be adjusted if the script is not run from the project root.
        # For now, assuming the script is run from the project root or path is absolute.

        # Construct path relative to this script's directory's parent if needed
        # For example:
        # current_dir = os.path.dirname(os.path.abspath(__file__))
        # project_root = os.path.dirname(current_dir)
        # absolute_seed_file_path = os.path.join(project_root, self.seed_file_path)
        # For now, directly using the provided path, assuming it's correct.

        try:
            with open(self.seed_file_path, 'r') as f:
                urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        except FileNotFoundError:
            print(f"Error: Seed file not found at {self.seed_file_path}")
            # Try path relative to project root if script is in scripts/
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            alt_path = os.path.join(project_root, self.seed_file_path)
            try:
                with open(alt_path, 'r') as f:
                    urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                print(f"Found seed file at alternative path: {alt_path}")
            except FileNotFoundError:
                print(f"Error: Seed file also not found at alternative path {alt_path}")
                return 0


        loaded_count = 0
        for url in urls:
            if self._is_valid_url(url):
                try:
                    seed_url = URLFrontier(
                        url=url,
                        priority=10,  # High priority for directory sites
                        source_type='seed_directory',
                        discovered_at=datetime.now(datetime.UTC),
                        status='pending'
                    )
                    self.session.add(seed_url)
                    loaded_count += 1
                except Exception as e:
                    # It's good practice to log the URL that caused the error
                    print(f"Error loading URL '{url}': {e}")
            else:
                print(f"Skipping invalid URL: {url}")


        try:
            self.session.commit()
            print(f"Loaded {loaded_count} directory websites as seed URLs into the database.")
        except Exception as e:
            self.session.rollback()
            print(f"Error committing to database: {e}. Rolled back changes.")
            # Potentially re-raise or handle more gracefully depending on requirements
            return 0 # Or indicate failure
        finally:
            self.session.close()
            print("Database session closed.")

        return loaded_count

    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format"""
        try:
            parsed = urlparse(url)
            # Check if scheme and netloc are present (basic validation)
            return all([parsed.scheme, parsed.netloc])
        except Exception:
            # Catch any exception during parsing, e.g., for very malformed URLs
            return False

# Usage
if __name__ == "__main__":
    # The path should be relative to the project root if the script is run from there.
    # If this script (seed_data_loader.py) is in the 'scripts' directory,
    # and 'directory_link_source' is at the project root,
    # the relative path from 'scripts/' would be '../directory_link_source/links_to_directory_websites.txt'.
    # However, the problem description implies the script is run from the root or the path is direct.
    # For robustness, let's try to make the path relative to the script's location.

    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    seed_file_relative_path = "directory_link_source/links_to_directory_websites.txt"
    actual_seed_file_path = os.path.join(project_root, seed_file_relative_path)

    # Check if sys.path needs adjustment if models/config are not found
    # This is often needed if scripts are run directly from a subdirectory
    sys.path.append(project_root)

    print(f"Attempting to load seed data from: {actual_seed_file_path}")

    # Ensure models and config can be imported
    try:
        from models.url_frontier import URLFrontier
        from config.database import SessionLocal
    except ImportError as e:
        print(f"Failed to import necessary modules. Ensure that the 'models' and 'config' directories are in PYTHONPATH.")
        print(f"Current sys.path: {sys.path}")
        print(f"Error: {e}")
        # Depending on how the environment is set up, you might need to adjust sys.path
        # For example, if 'universal_web_directory' is the root package:
        # import sys
        # sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
        exit(1) # Exit if essential imports fail

    loader = SeedDataLoader(actual_seed_file_path)
    count = loader.load_directory_websites()
    if count > 0:
        print(f"Successfully processed {count} URLs.")
    else:
        print("No URLs were loaded. Check logs for errors.")
