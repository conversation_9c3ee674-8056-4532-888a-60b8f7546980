#!/usr/bin/env python3
"""
Seed Data Loader - Milestone 0.2 Implementation
Loads directory websites from links_to_directory_websites.txt as high-priority seed URLs
"""

import os
import sys
from urllib.parse import urlparse
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.database import get_db_session
from models import URLFrontier, Site
from core.url_discovery import URLDiscoveryEngine

class SeedDataLoader:
    def __init__(self, seed_file_path: str):
        self.seed_file_path = seed_file_path
        self.url_discovery = URLDiscoveryEngine()

    def load_directory_websites(self):
        """Load directory websites as high-priority seed URLs"""
        try:
            with open(self.seed_file_path, 'r') as f:
                urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        except FileNotFoundError:
            print(f"Error: Seed file not found at {self.seed_file_path}")
            return 0

        loaded_count = 0
        total_urls = len(urls)
        print(f"Loading {total_urls} URLs from seed file...")

        # Process all URLs
        print(f"Processing all {total_urls} URLs...")

        for i, url in enumerate(urls, 1):
            print(f"[{i}/{total_urls}] Processing: {url}")
            if self._is_valid_url(url):
                try:
                    # Use the URL discovery engine to add seed URLs
                    success = self.url_discovery.add_seed_url(url, priority=10)
                    if success:
                        loaded_count += 1
                        print(f"  ✓ Added successfully")
                    else:
                        print(f"  ✗ Failed to add (may already exist)")
                except Exception as e:
                    print(f"  ✗ Error: {e}")
            else:
                print(f"  ✗ Invalid URL format")

        print(f"\n🎉 Successfully loaded {loaded_count}/{total_urls} directory websites as seed URLs")
        return loaded_count

    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format"""
        try:
            parsed = urlparse(url)
            # Check if scheme and netloc are present (basic validation)
            return all([parsed.scheme, parsed.netloc])
        except Exception:
            # Catch any exception during parsing, e.g., for very malformed URLs
            return False

# Usage
if __name__ == "__main__":
    # Construct path to seed file
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    seed_file_path = project_root / "directory_link_source" / "links_to_directory_websites.txt"

    print(f"🌱 Loading seed data from: {seed_file_path}")

    loader = SeedDataLoader(str(seed_file_path))
    count = loader.load_directory_websites()

    if count > 0:
        print(f"🎉 Successfully processed {count} directory website URLs as seed data!")
    else:
        print("❌ No URLs were loaded. Check logs for errors.")
