#!/usr/bin/env python3
"""
Verification script for Milestone 1.3: Advanced Rate Limiting & Politeness System

This script verifies that all components of the advanced rate limiting system
are properly implemented and working correctly.
"""

import sys
import os
import asyncio
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import get_db_session
from services.crawler.rate_limiter import AdaptiveRateLimiter
from models.rate_limit_tracking import RateLimitTracking
from models.request_history import RequestHistory
from sqlalchemy import text
from datetime import datetime, timedelta

def test_database_schema():
    """Test 1: Verify database tables exist with correct schema"""
    print("🔍 Test 1: Database Schema Verification")
    
    with next(get_db_session()) as session:
        # Test rate_limit_tracking table
        try:
            result = session.execute(text("SELECT COUNT(*) FROM rate_limit_tracking"))
            rate_limit_count = result.scalar()
            print(f"  ✅ rate_limit_tracking table exists (current records: {rate_limit_count})")
        except Exception as e:
            print(f"  ❌ rate_limit_tracking table error: {e}")
            return False
        
        # Test request_history table
        try:
            result = session.execute(text("SELECT COUNT(*) FROM request_history"))
            history_count = result.scalar()
            print(f"  ✅ request_history table exists (current records: {history_count})")
        except Exception as e:
            print(f"  ❌ request_history table error: {e}")
            return False
    
    return True

def test_rate_limiter_initialization():
    """Test 2: Verify AdaptiveRateLimiter initialization"""
    print("\n🔍 Test 2: AdaptiveRateLimiter Initialization")
    
    with next(get_db_session()) as session:
        try:
            limiter = AdaptiveRateLimiter(session, worker_id="test-worker-1")
            
            print(f"  ✅ AdaptiveRateLimiter created successfully")
            print(f"      - Worker ID: {limiter.worker_id}")
            print(f"      - Default delay: {limiter.default_delay_ms}ms")
            print(f"      - Max delay: {limiter.max_delay_ms}ms")
            print(f"      - Min delay: {limiter.min_delay_ms}ms")
            print(f"      - Adaptive factor increase: {limiter.adaptive_factor_increase}")
            print(f"      - Adaptive factor decrease: {limiter.adaptive_factor_decrease}")
            
        except Exception as e:
            print(f"  ❌ AdaptiveRateLimiter initialization error: {e}")
            return False
    
    return True

async def test_rate_limiting_functionality():
    """Test 3: Test basic rate limiting functionality"""
    print("\n🔍 Test 3: Rate Limiting Functionality")
    
    with next(get_db_session()) as session:
        limiter = AdaptiveRateLimiter(session, worker_id="test-worker-2")
        test_domain = "test-rate-limit.example"
        
        try:
            # Clean up any existing test data
            existing = session.query(RateLimitTracking).filter_by(
                domain=test_domain, worker_id="test-worker-2"
            ).first()
            if existing:
                session.delete(existing)
                session.commit()
            
            # Test first request (should not be delayed)
            start_time = time.time()
            delay1 = await limiter.wait_if_needed(test_domain)
            elapsed1 = time.time() - start_time
            
            print(f"  ✅ First request delay: {delay1:.3f}s (actual: {elapsed1:.3f}s)")
            
            # Test second request (should be delayed)
            start_time = time.time()
            delay2 = await limiter.wait_if_needed(test_domain)
            elapsed2 = time.time() - start_time
            
            print(f"  ✅ Second request delay: {delay2:.3f}s (actual: {elapsed2:.3f}s)")
            
            # Verify rate limit state was created
            rate_state = session.query(RateLimitTracking).filter_by(
                domain=test_domain, worker_id="test-worker-2"
            ).first()
            
            if rate_state:
                print(f"  ✅ Rate limit state created in database")
                print(f"      - Domain: {rate_state.domain}")
                print(f"      - Worker ID: {rate_state.worker_id}")
                print(f"      - Current delay: {rate_state.current_delay_ms}ms")
                print(f"      - Next allowed: {rate_state.next_allowed_request}")
            else:
                print(f"  ❌ Rate limit state not found in database")
                return False
            
            # Cleanup
            session.delete(rate_state)
            session.commit()
            
        except Exception as e:
            print(f"  ❌ Rate limiting functionality error: {e}")
            return False
    
    return True

def test_request_history_logging():
    """Test 4: Test request history logging"""
    print("\n🔍 Test 4: Request History Logging")
    
    with next(get_db_session()) as session:
        limiter = AdaptiveRateLimiter(session, worker_id="test-worker-3")
        test_domain = "test-history.example"
        test_url = f"https://{test_domain}/test-page"
        
        try:
            # Clean up any existing test data
            existing_history = session.query(RequestHistory).filter_by(
                domain=test_domain, worker_id="test-worker-3"
            ).all()
            for record in existing_history:
                session.delete(record)
            session.commit()
            
            # Test successful request logging
            limiter.record_request_result(
                domain=test_domain,
                url=test_url,
                success=True,
                response_time_ms=250,
                status_code=200,
                content_length=1024
            )
            
            # Test failed request logging
            limiter.record_request_result(
                domain=test_domain,
                url=test_url,
                success=False,
                response_time_ms=5000,
                status_code=500,
                error_type="server_error",
                error_message="Internal server error"
            )
            
            # Verify history records were created
            history_records = session.query(RequestHistory).filter_by(
                domain=test_domain, worker_id="test-worker-3"
            ).all()
            
            if len(history_records) == 2:
                print(f"  ✅ Request history logging working")
                print(f"      - Records created: {len(history_records)}")
                
                success_record = next((r for r in history_records if r.success), None)
                failure_record = next((r for r in history_records if not r.success), None)
                
                if success_record:
                    print(f"      - Success record: status={success_record.status_code}, time={success_record.response_time_ms}ms")
                
                if failure_record:
                    print(f"      - Failure record: status={failure_record.status_code}, error={failure_record.error_type}")
            else:
                print(f"  ❌ Expected 2 history records, found {len(history_records)}")
                return False
            
            # Cleanup
            for record in history_records:
                session.delete(record)
            session.commit()
            
        except Exception as e:
            print(f"  ❌ Request history logging error: {e}")
            return False
    
    return True

def test_adaptive_rate_limiting():
    """Test 5: Test adaptive rate limiting behavior"""
    print("\n🔍 Test 5: Adaptive Rate Limiting")
    
    with next(get_db_session()) as session:
        limiter = AdaptiveRateLimiter(session, worker_id="test-worker-4")
        test_domain = "test-adaptive.example"
        
        try:
            # Clean up any existing test data
            existing = session.query(RateLimitTracking).filter_by(
                domain=test_domain, worker_id="test-worker-4"
            ).first()
            if existing:
                session.delete(existing)
                session.commit()
            
            # Create initial rate limit state
            rate_state = RateLimitTracking(
                domain=test_domain,
                worker_id="test-worker-4",
                current_delay_ms=1000,
                consecutive_failures=0
            )
            session.add(rate_state)
            session.commit()
            
            initial_delay = rate_state.current_delay_ms
            print(f"  ✅ Initial delay: {initial_delay}ms")
            
            # Test successful request (should decrease delay)
            limiter.record_request_result(
                domain=test_domain,
                url=f"https://{test_domain}/success",
                success=True,
                response_time_ms=200,
                status_code=200
            )
            
            session.refresh(rate_state)
            success_delay = rate_state.current_delay_ms
            print(f"  ✅ Delay after success: {success_delay}ms (decreased: {success_delay < initial_delay})")
            
            # Test failed request (should increase delay)
            limiter.record_request_result(
                domain=test_domain,
                url=f"https://{test_domain}/failure",
                success=False,
                response_time_ms=5000,
                status_code=500,
                error_type="server_error"
            )
            
            session.refresh(rate_state)
            failure_delay = rate_state.current_delay_ms
            print(f"  ✅ Delay after failure: {failure_delay}ms (increased: {failure_delay > success_delay})")
            print(f"  ✅ Consecutive failures: {rate_state.consecutive_failures}")
            
            # Cleanup
            session.delete(rate_state)
            session.commit()
            
        except Exception as e:
            print(f"  ❌ Adaptive rate limiting error: {e}")
            return False
    
    return True

async def main():
    """Run all verification tests"""
    print("🚀 Starting Milestone 1.3: Advanced Rate Limiting & Politeness System Verification")
    print("=" * 80)
    
    tests = [
        test_database_schema,
        test_rate_limiter_initialization,
        test_rate_limiting_functionality,
        test_request_history_logging,
        test_adaptive_rate_limiting,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if asyncio.iscoroutinefunction(test):
                result = await test()
            else:
                result = test()
            
            if result:
                passed += 1
            else:
                print(f"  ❌ Test failed")
        except Exception as e:
            print(f"  ❌ Test error: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 VERIFICATION RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ✅ Milestone 1.3: Advanced Rate Limiting & Politeness System FULLY IMPLEMENTED!")
        return True
    else:
        print("❌ Some tests failed. Implementation incomplete.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
