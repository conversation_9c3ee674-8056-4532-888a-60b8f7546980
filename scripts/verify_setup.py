#!/usr/bin/env python3
"""
Verification script for Universal Web Directory environment setup
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_python_version():
    """Check Python version"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 12:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} - Need Python 3.12+")
        return False

def check_dependencies():
    """Check required dependencies"""
    print("\n📦 Checking dependencies...")
    
    dependencies = [
        ('requests', 'Web scraping'),
        ('bs4', 'HTML parsing (beautifulsoup4)'),
        ('lxml', 'XML/HTML parser'),
        ('sqlalchemy', 'Database ORM'),
        ('psycopg2', 'PostgreSQL adapter'),
        ('alembic', 'Database migrations'),
        ('pytest', 'Testing framework')
    ]
    
    all_good = True
    for package, description in dependencies:
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ❌ {package} - {description} (MISSING)")
            all_good = False
    
    return all_good

def check_models():
    """Check if models can be imported"""
    print("\n🏗️  Checking database models...")
    
    try:
        from models import Site, URLFrontier, Page, Link, CrawlLog
        print("   ✅ All models imported successfully")
        return True
    except Exception as e:
        print(f"   ❌ Model import failed: {e}")
        return False

def check_database_config():
    """Check database configuration"""
    print("\n🗄️  Checking database configuration...")
    
    try:
        from config.database import DatabaseConfig
        config = DatabaseConfig()
        
        print(f"   📍 Host: {config.host}")
        print(f"   📍 Port: {config.port}")
        print(f"   📍 Database: {config.database}")
        print(f"   📍 User: {config.username}")
        
        if config.password == 'secure_password_here':
            print("   ⚠️  Using default password - consider setting DB_PASSWORD environment variable")
        else:
            print("   ✅ Custom password configured")
        
        return True
    except Exception as e:
        print(f"   ❌ Database config failed: {e}")
        return False

def check_project_structure():
    """Check project structure"""
    print("\n📁 Checking project structure...")
    
    required_dirs = [
        'config',
        'models', 
        'database/schema',
        'scripts',
        'tests'
    ]
    
    required_files = [
        'config/database.py',
        'models/__init__.py',
        'database/schema/01_initial_setup.sql',
        'scripts/setup_database.py',
        'tests/conftest.py',
        'tests/test_database_setup.py',
        'requirements.txt',
        '.env.example'
    ]
    
    all_good = True
    
    for directory in required_dirs:
        if (project_root / directory).exists():
            print(f"   ✅ {directory}/")
        else:
            print(f"   ❌ {directory}/ (MISSING)")
            all_good = False
    
    for file_path in required_files:
        if (project_root / file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (MISSING)")
            all_good = False
    
    return all_good

def run_basic_test():
    """Run a basic database model test"""
    print("\n🧪 Running basic model test...")
    
    try:
        import pytest
        result = pytest.main(['-v', 'tests/test_database_setup.py::test_site_creation', '-q'])
        
        if result == 0:
            print("   ✅ Basic test passed")
            return True
        else:
            print("   ❌ Basic test failed")
            return False
    except Exception as e:
        print(f"   ❌ Test execution failed: {e}")
        return False

def main():
    """Main verification function"""
    print("🚀 Universal Web Directory - Environment Setup Verification")
    print("=" * 60)
    
    checks = [
        check_python_version,
        check_dependencies,
        check_models,
        check_database_config,
        check_project_structure,
        run_basic_test
    ]
    
    results = []
    for check in checks:
        results.append(check())
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 ALL CHECKS PASSED ({passed}/{total})")
        print("\n✅ Environment setup is complete!")
        print("✅ Ready to proceed with Phase 0 implementation")
        print("\nNext steps:")
        print("1. Set up PostgreSQL database (if not already done)")
        print("2. Run: python scripts/setup_database.py")
        print("3. Start implementing core crawling functionality")
        return True
    else:
        print(f"⚠️  SOME CHECKS FAILED ({passed}/{total})")
        print("\n❌ Environment setup needs attention")
        print("Please fix the issues above before proceeding")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
