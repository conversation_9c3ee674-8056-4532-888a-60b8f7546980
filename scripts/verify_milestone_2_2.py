#!/usr/bin/env python3
"""
Comprehensive verification script for Milestone 2.2: Entity Recognition & Knowledge Extraction
Tests all components of the entity extraction and relationship mapping system.
"""

import sys
import os
import asyncio
import logging
from typing import Dict, List

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import get_db_session
from sqlalchemy import text
from services.nlp.entity_extractor import EntityExtractor
from services.nlp.entity_integration_service import EntityIntegrationService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Milestone22Verifier:
    """Comprehensive verifier for Milestone 2.2: Entity Recognition & Knowledge Extraction"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.session = None
    
    def _get_session(self):
        """Get database session"""
        if not self.session:
            self.session = next(get_db_session())
        return self.session
    
    def _test_passed(self, test_name: str):
        """Mark test as passed"""
        self.total_tests += 1
        self.passed_tests += 1
        logger.info(f"✅ {test_name} - PASSED")
    
    def _test_failed(self, test_name: str, error: str = ""):
        """Mark test as failed"""
        self.total_tests += 1
        logger.error(f"❌ {test_name} - FAILED: {error}")
    
    async def verify_database_schema(self):
        """Test 1: Verify entity database schema is deployed"""
        logger.info("🔍 Test 1: Verifying Entity Database Schema")
        
        session = self._get_session()
        
        try:
            # Check if all entity tables exist
            required_tables = ['entities', 'page_entities', 'entity_relationships']
            
            for table in required_tables:
                result = session.execute(text(f"""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = '{table}'
                """)).fetchone()[0]
                
                if result == 0:
                    self._test_failed("Database Schema", f"Table {table} not found")
                    return
            
            # Check table structures
            entities_columns = session.execute(text("""
                SELECT column_name FROM information_schema.columns 
                WHERE table_name = 'entities' AND table_schema = 'public'
            """)).fetchall()
            
            required_entity_columns = ['id', 'name', 'entity_type', 'canonical_name', 'confidence_score', 'frequency_count']
            entity_column_names = [col[0] for col in entities_columns]
            
            for col in required_entity_columns:
                if col not in entity_column_names:
                    self._test_failed("Database Schema", f"Column {col} not found in entities table")
                    return
            
            self._test_passed("Database Schema - All entity tables and columns exist")
            
        except Exception as e:
            self._test_failed("Database Schema", str(e))
    
    async def verify_sqlalchemy_models(self):
        """Test 2: Verify SQLAlchemy entity models are working"""
        logger.info("🔍 Test 2: Verifying SQLAlchemy Entity Models")
        
        try:
            # Import models
            from models.entities import Entity, PageEntity, EntityRelationship
            
            session = self._get_session()
            
            # Test basic model queries
            entity_count = session.query(Entity).count()
            page_entity_count = session.query(PageEntity).count()
            relationship_count = session.query(EntityRelationship).count()
            
            logger.info(f"   Entity records: {entity_count}")
            logger.info(f"   Page-entity records: {page_entity_count}")
            logger.info(f"   Relationship records: {relationship_count}")
            
            # Test model relationships
            if entity_count > 0:
                sample_entity = session.query(Entity).first()
                if hasattr(sample_entity, 'page_mentions'):
                    logger.info(f"   ✅ Entity model has page_mentions relationship")
                else:
                    logger.warning(f"   ⚠️ Entity model missing page_mentions relationship")
            
            self._test_passed("SQLAlchemy Entity Models - Models working correctly")
            
        except Exception as e:
            self._test_failed("SQLAlchemy Entity Models", str(e))
    
    async def verify_entity_extractor(self):
        """Test 3: Verify EntityExtractor service is working"""
        logger.info("🔍 Test 3: Verifying EntityExtractor Service")
        
        try:
            session = self._get_session()
            extractor = EntityExtractor(session)
            
            # Test with sample content
            test_data = {
                'page_id': '550e8400-e29b-41d4-a716-446655440000',  # This will fail FK constraint, but we test extraction logic
                'content_text': 'Apple Inc. was founded by Steve Jobs in Cupertino, California. Microsoft Corporation is another major technology company based in Redmond, Washington.'
            }
            
            # Test entity extraction logic (without database storage)
            doc = extractor.nlp(test_data['content_text'])
            entities = extractor._extract_entities_from_doc(doc)
            
            if len(entities) > 0:
                logger.info(f"   ✅ Extracted {len(entities)} entities")
                
                # Check entity types
                entity_types = set(entity['label'] for entity in entities)
                logger.info(f"   ✅ Entity types found: {entity_types}")
                
                # Test relationship finding
                relationships = extractor._find_entity_relationships(doc, entities)
                logger.info(f"   ✅ Found {len(relationships)} relationships")
                
                self._test_passed("EntityExtractor Service - Extraction logic working")
            else:
                self._test_failed("EntityExtractor Service", "No entities extracted from test content")
            
        except Exception as e:
            self._test_failed("EntityExtractor Service", str(e))
    
    async def verify_entity_integration_service(self):
        """Test 4: Verify EntityIntegrationService is working"""
        logger.info("🔍 Test 4: Verifying EntityIntegrationService")
        
        try:
            service = EntityIntegrationService()
            
            # Test statistics
            stats = await service.get_entity_statistics()
            
            if 'error' not in stats:
                logger.info(f"   ✅ Statistics retrieved successfully")
                logger.info(f"   Total entities: {stats.get('total_entities', 0)}")
                logger.info(f"   Total relationships: {stats.get('total_relationships', 0)}")
                
                # Test entity search
                if stats.get('total_entities', 0) > 0:
                    entities = await service.find_entity_by_name('Apple')
                    logger.info(f"   ✅ Entity search working - found {len(entities)} matches for 'Apple'")
                
                self._test_passed("EntityIntegrationService - Service working correctly")
            else:
                self._test_failed("EntityIntegrationService", stats.get('error', 'Unknown error'))
            
            service.close()
            
        except Exception as e:
            self._test_failed("EntityIntegrationService", str(e))
    
    async def verify_end_to_end_pipeline(self):
        """Test 5: Verify end-to-end entity extraction pipeline"""
        logger.info("🔍 Test 5: Verifying End-to-End Entity Pipeline")
        
        try:
            session = self._get_session()
            
            # Get a real page from database
            result = session.execute(text("""
                SELECT id, title, content_text 
                FROM pages 
                WHERE content_text IS NOT NULL 
                AND LENGTH(content_text) > 100 
                LIMIT 1
            """)).fetchone()
            
            if not result:
                self._test_failed("End-to-End Pipeline", "No suitable pages found for testing")
                return
            
            page_id, title, content_text = result
            
            # Count entities before
            entities_before = session.execute(text("SELECT COUNT(*) FROM entities")).fetchone()[0]
            
            # Process page with EntityIntegrationService
            service = EntityIntegrationService()
            
            page_data = {
                'content_text': content_text[:1000],  # First 1000 chars
                'title': title or '',
                'url': 'http://test.example.com'
            }
            
            result = await service.process_page_entities(str(page_id), page_data)
            
            if result and result.get('processing_successful'):
                # Count entities after
                entities_after = session.execute(text("SELECT COUNT(*) FROM entities")).fetchone()[0]
                
                logger.info(f"   ✅ Pipeline processed successfully")
                logger.info(f"   Entities found: {result.get('entities_found', 0)}")
                logger.info(f"   Relationships found: {result.get('relationships_found', 0)}")
                logger.info(f"   Entities in DB before: {entities_before}, after: {entities_after}")
                
                self._test_passed("End-to-End Pipeline - Complete workflow working")
            else:
                self._test_failed("End-to-End Pipeline", f"Processing failed: {result}")
            
            service.close()
            
        except Exception as e:
            self._test_failed("End-to-End Pipeline", str(e))
    
    async def verify_data_quality(self):
        """Test 6: Verify data quality and relationships"""
        logger.info("🔍 Test 6: Verifying Data Quality and Relationships")
        
        try:
            session = self._get_session()
            
            # Check for duplicate entities
            duplicates = session.execute(text("""
                SELECT canonical_name, entity_type, COUNT(*) as count
                FROM entities
                GROUP BY canonical_name, entity_type
                HAVING COUNT(*) > 1
            """)).fetchall()
            
            if len(duplicates) == 0:
                logger.info(f"   ✅ No duplicate entities found")
            else:
                logger.warning(f"   ⚠️ Found {len(duplicates)} potential duplicate entities")
            
            # Check relationship consistency
            orphaned_relationships = session.execute(text("""
                SELECT COUNT(*) FROM entity_relationships er
                WHERE NOT EXISTS (SELECT 1 FROM entities e1 WHERE e1.id = er.entity1_id)
                OR NOT EXISTS (SELECT 1 FROM entities e2 WHERE e2.id = er.entity2_id)
            """)).fetchone()[0]
            
            if orphaned_relationships == 0:
                logger.info(f"   ✅ No orphaned relationships found")
            else:
                logger.warning(f"   ⚠️ Found {orphaned_relationships} orphaned relationships")
            
            # Check page-entity consistency
            orphaned_page_entities = session.execute(text("""
                SELECT COUNT(*) FROM page_entities pe
                WHERE NOT EXISTS (SELECT 1 FROM pages p WHERE p.id = pe.page_id)
                OR NOT EXISTS (SELECT 1 FROM entities e WHERE e.id = pe.entity_id)
            """)).fetchone()[0]
            
            if orphaned_page_entities == 0:
                logger.info(f"   ✅ No orphaned page-entity relationships found")
                self._test_passed("Data Quality - All relationships are consistent")
            else:
                logger.warning(f"   ⚠️ Found {orphaned_page_entities} orphaned page-entity relationships")
                self._test_failed("Data Quality", f"Found {orphaned_page_entities} orphaned relationships")
            
        except Exception as e:
            self._test_failed("Data Quality", str(e))
    
    async def run_all_tests(self):
        """Run all verification tests"""
        logger.info("🧠 Starting Milestone 2.2: Entity Recognition & Knowledge Extraction Verification")
        logger.info("=" * 80)
        
        # Run all tests
        await self.verify_database_schema()
        await self.verify_sqlalchemy_models()
        await self.verify_entity_extractor()
        await self.verify_entity_integration_service()
        await self.verify_end_to_end_pipeline()
        await self.verify_data_quality()
        
        # Print summary
        logger.info("=" * 80)
        logger.info(f"📊 VERIFICATION SUMMARY")
        logger.info(f"Total Tests: {self.total_tests}")
        logger.info(f"Passed: {self.passed_tests}")
        logger.info(f"Failed: {self.total_tests - self.passed_tests}")
        logger.info(f"Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        if self.passed_tests == self.total_tests:
            logger.info("🎉 ALL TESTS PASSED - Milestone 2.2 is FULLY IMPLEMENTED!")
            return True
        else:
            logger.warning(f"⚠️ {self.total_tests - self.passed_tests} tests failed - Implementation needs attention")
            return False
    
    def close(self):
        """Close database session"""
        if self.session:
            self.session.close()


async def main():
    """Main verification function"""
    verifier = Milestone22Verifier()
    
    try:
        success = await verifier.run_all_tests()
        return success
    finally:
        verifier.close()


if __name__ == '__main__':
    asyncio.run(main())
