#!/usr/bin/env python3
"""
Database setup script for Universal Web Directory
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.database import DatabaseConfig, engine as db_engine, SessionLocal as db_SessionLocal
from models import Base, Site, URLFrontier, Page, Link, CrawlLog
import subprocess

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_postgresql_connection():
    """Check if PostgreSQL is running and accessible"""
    config = DatabaseConfig()
    
    try:
        # Try to connect using psql
        cmd = [
            'psql', 
            '-h', config.host,
            '-p', config.port,
            '-U', 'postgres',
            '-c', 'SELECT version();'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logger.info("PostgreSQL connection successful")
            return True
        else:
            logger.error(f"PostgreSQL connection failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("PostgreSQL connection timed out")
        return False
    except Exception as e:
        logger.error(f"Error checking PostgreSQL connection: {e}")
        return False

def create_database_and_user():
    """Create database and user if they don't exist"""
    config = DatabaseConfig()
    
    try:
        # Create database
        create_db_cmd = [
            'psql',
            '-h', config.host,
            '-p', config.port,
            '-U', 'postgres',
            '-c', f"CREATE DATABASE {config.database};"
        ]
        
        result = subprocess.run(create_db_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"Database '{config.database}' created successfully")
        elif "already exists" in result.stderr:
            logger.info(f"Database '{config.database}' already exists")
        else:
            logger.warning(f"Database creation result: {result.stderr}")
        
        # Create user
        create_user_cmd = [
            'psql',
            '-h', config.host,
            '-p', config.port,
            '-U', 'postgres',
            '-c', f"CREATE USER {config.username} WITH PASSWORD '{config.password}';"
        ]
        
        result = subprocess.run(create_user_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"User '{config.username}' created successfully")
        elif "already exists" in result.stderr:
            logger.info(f"User '{config.username}' already exists")
        else:
            logger.warning(f"User creation result: {result.stderr}")
        
        # Grant privileges
        grant_cmd = [
            'psql',
            '-h', config.host,
            '-p', config.port,
            '-U', 'postgres',
            '-c', f"GRANT ALL PRIVILEGES ON DATABASE {config.database} TO {config.username};"
        ]
        
        result = subprocess.run(grant_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("Privileges granted successfully")
        else:
            logger.warning(f"Grant privileges result: {result.stderr}")
            
        return True
        
    except Exception as e:
        logger.error(f"Error creating database and user: {e}")
        return False

def run_sql_file(sql_file_path):
    """Run SQL file against the database"""
    config = DatabaseConfig()
    
    try:
        cmd = [
            'psql',
            '-h', config.host,
            '-p', config.port,
            '-U', config.username,
            '-d', config.database,
            '-f', str(sql_file_path)
        ]
        
        # Set password via environment variable
        env = os.environ.copy()
        env['PGPASSWORD'] = config.password
        
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        
        if result.returncode == 0:
            logger.info(f"SQL file '{sql_file_path}' executed successfully")
            return True
        else:
            logger.error(f"Error executing SQL file: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Error running SQL file: {e}")
        return False

def create_tables_with_sqlalchemy():
    """Create tables using SQLAlchemy models"""
    try:
        # Use the imported engine
        Base.metadata.create_all(bind=db_engine)
        
        logger.info("Tables created successfully with SQLAlchemy")
        return True
        
    except Exception as e:
        logger.error(f"Error creating tables with SQLAlchemy: {e}")
        return False

def main():
    """Main setup function"""
    logger.info("Starting database setup...")
    
    # Check PostgreSQL connection
    if not os.getenv("SKIP_PSQL_COMMANDS"):
        if not check_postgresql_connection():
            logger.error("Cannot connect to PostgreSQL. Please ensure it's running.")
            return False

        # Create database and user
        if not create_database_and_user():
            logger.error("Failed to create database and user")
            return False

        # Run initial SQL setup
        sql_file = project_root / 'database' / 'schema' / '01_initial_setup.sql'
        if sql_file.exists():
            if not run_sql_file(sql_file):
                logger.error("Failed to run initial SQL setup")
                return False
        else:
            logger.warning(f"SQL file not found: {sql_file}")
    
    # Create tables with SQLAlchemy (as backup/verification)
    # This is essential for the script to succeed when SKIP_PSQL_COMMANDS is set
    if not create_tables_with_sqlalchemy():
        logger.error("Failed to create tables with SQLAlchemy")
        return False
    
    # Run rate limiting SQL setup
    if not os.getenv("SKIP_PSQL_COMMANDS"):
        rate_limiting_sql_file = project_root / 'database' / 'schema' / '03_rate_limiting.sql'
        if rate_limiting_sql_file.exists():
            if not run_sql_file(rate_limiting_sql_file):
                logger.error("Failed to run rate limiting SQL setup")
                return False
        else:
            logger.warning(f"SQL file not found: {rate_limiting_sql_file}")

    logger.info("Database setup completed successfully!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
