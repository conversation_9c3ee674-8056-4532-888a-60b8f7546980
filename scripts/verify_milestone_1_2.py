#!/usr/bin/env python3
"""
Verification script for Milestone 1.2: Robots.txt Compliance System

This script verifies that all components of the robots.txt compliance system
are properly implemented and working correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import get_db_session
from services.crawler.robots_handler import RobotsHandler
from models.robots_cache import RobotsCache
from models.domain_policies import DomainPolicy
import requests
from datetime import datetime, timedelta
from sqlalchemy import text

def test_database_schema():
    """Test 1: Verify database tables exist with correct schema"""
    print("🔍 Test 1: Database Schema Verification")
    
    with next(get_db_session()) as session:
        # Test robots_cache table
        try:
            result = session.execute(text("SELECT COUNT(*) FROM robots_cache"))
            robots_count = result.scalar()
            print(f"  ✅ robots_cache table exists (current records: {robots_count})")
        except Exception as e:
            print(f"  ❌ robots_cache table error: {e}")
            return False

        # Test domain_policies table
        try:
            result = session.execute(text("SELECT COUNT(*) FROM domain_policies"))
            policies_count = result.scalar()
            print(f"  ✅ domain_policies table exists (current records: {policies_count})")
        except Exception as e:
            print(f"  ❌ domain_policies table error: {e}")
            return False
    
    return True

def test_robots_handler_service():
    """Test 2: Verify RobotsHandler service functionality"""
    print("\n🔍 Test 2: RobotsHandler Service Verification")
    
    with next(get_db_session()) as session:
        handler = RobotsHandler(session)
        
        # Test basic functionality
        test_domain = "example.com"
        
        try:
            # Test can_crawl method
            can_crawl = handler.can_crawl(f"https://{test_domain}/test")
            print(f"  ✅ can_crawl() method works: {can_crawl}")
            
            # Test get_crawl_delay method
            delay = handler.get_crawl_delay(test_domain)
            print(f"  ✅ get_crawl_delay() method works: {delay}")
            
            # Test get_sitemaps method
            sitemaps = handler.get_sitemaps(test_domain)
            print(f"  ✅ get_sitemaps() method works: {sitemaps}")
            
        except Exception as e:
            print(f"  ❌ RobotsHandler service error: {e}")
            return False
    
    return True

def test_robots_txt_fetching():
    """Test 3: Test actual robots.txt fetching and caching"""
    print("\n🔍 Test 3: Robots.txt Fetching and Caching")
    
    with next(get_db_session()) as session:
        handler = RobotsHandler(session)
        
        # Test with a real domain that has robots.txt
        test_domain = "httpbin.org"
        test_url = f"https://{test_domain}/robots.txt"
        
        try:
            # Check if robots.txt is accessible
            response = requests.get(test_url, timeout=5)
            if response.status_code != 200:
                print(f"  ⚠️  Skipping test - {test_domain} robots.txt not accessible")
                return True
            
            # Test fetching and caching
            robots_data = handler._get_robots_data(test_domain)
            
            if robots_data:
                print(f"  ✅ Successfully fetched robots.txt for {test_domain}")
                print(f"  ✅ Cache entry created: accessible={robots_data.is_accessible}")
                
                # Verify cache entry in database
                cached = session.query(RobotsCache).filter_by(domain=test_domain).first()
                if cached:
                    print(f"  ✅ Database cache entry verified")
                    print(f"      - Domain: {cached.domain}")
                    print(f"      - Fetched at: {cached.fetched_at}")
                    print(f"      - Expires at: {cached.expires_at}")
                    print(f"      - Is accessible: {cached.is_accessible}")
                else:
                    print(f"  ❌ Cache entry not found in database")
                    return False
            else:
                print(f"  ❌ Failed to fetch robots.txt for {test_domain}")
                return False
                
        except Exception as e:
            print(f"  ❌ Robots.txt fetching error: {e}")
            return False
    
    return True

def test_domain_policies():
    """Test 4: Test domain policies functionality"""
    print("\n🔍 Test 4: Domain Policies Functionality")
    
    with next(get_db_session()) as session:
        handler = RobotsHandler(session)
        
        # Create a test domain policy
        test_domain = "test-domain.example"
        
        try:
            # Check if policy already exists
            existing_policy = session.query(DomainPolicy).filter_by(domain=test_domain).first()
            if existing_policy:
                session.delete(existing_policy)
                session.commit()
            
            # Create new policy
            policy = DomainPolicy(
                domain=test_domain,
                crawl_delay=5,
                max_concurrent_requests=2,
                requests_per_minute=30
            )
            session.add(policy)
            session.commit()
            
            print(f"  ✅ Created test domain policy for {test_domain}")
            
            # Test policy retrieval
            retrieved_policy = handler._get_domain_policy(test_domain)
            if retrieved_policy:
                print(f"  ✅ Successfully retrieved domain policy")
                print(f"      - Crawl delay: {retrieved_policy.crawl_delay}")
                print(f"      - Max concurrent: {retrieved_policy.max_concurrent_requests}")
                print(f"      - Requests per minute: {retrieved_policy.requests_per_minute}")
            else:
                print(f"  ❌ Failed to retrieve domain policy")
                return False
            
            # Test crawl delay integration
            delay = handler.get_crawl_delay(test_domain)
            if delay == 5:
                print(f"  ✅ Domain policy crawl delay integration works: {delay}")
            else:
                print(f"  ❌ Domain policy crawl delay mismatch: expected 5, got {delay}")
                return False
            
            # Cleanup
            session.delete(policy)
            session.commit()
            
        except Exception as e:
            print(f"  ❌ Domain policies error: {e}")
            return False
    
    return True

def test_robots_txt_parsing():
    """Test 5: Test robots.txt parsing functionality"""
    print("\n🔍 Test 5: Robots.txt Parsing")
    
    with next(get_db_session()) as session:
        handler = RobotsHandler(session)
        
        # Test robots.txt content
        test_robots_txt = """
User-agent: *
Disallow: /private/
Allow: /public/
Crawl-delay: 2

User-agent: Googlebot
Disallow: /admin/
Crawl-delay: 1

Sitemap: https://example.com/sitemap.xml
Sitemap: https://example.com/sitemap2.xml
"""
        
        try:
            parsed = handler._parse_robots_txt(test_robots_txt)
            
            print(f"  ✅ Successfully parsed robots.txt")
            print(f"      - User agents found: {len(parsed['user_agents'])}")
            print(f"      - Sitemaps found: {len(parsed['sitemaps'])}")
            print(f"      - Global crawl delay: {parsed['crawl_delay']}")
            
            # Verify specific rules
            if '*' in parsed['user_agents']:
                wildcard_rules = parsed['user_agents']['*']
                if '/private/' in wildcard_rules['disallow']:
                    print(f"  ✅ Disallow rule parsed correctly")
                if '/public/' in wildcard_rules['allow']:
                    print(f"  ✅ Allow rule parsed correctly")
                if wildcard_rules['crawl_delay'] == 2:
                    print(f"  ✅ User-agent specific crawl delay parsed correctly")
            
            if len(parsed['sitemaps']) == 2:
                print(f"  ✅ Sitemap URLs parsed correctly")
            
        except Exception as e:
            print(f"  ❌ Robots.txt parsing error: {e}")
            return False
    
    return True

def main():
    """Run all verification tests"""
    print("🚀 Starting Milestone 1.2: Robots.txt Compliance System Verification")
    print("=" * 70)
    
    tests = [
        test_database_schema,
        test_robots_handler_service,
        test_robots_txt_fetching,
        test_domain_policies,
        test_robots_txt_parsing,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"  ❌ Test failed")
        except Exception as e:
            print(f"  ❌ Test error: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 VERIFICATION RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ✅ Milestone 1.2: Robots.txt Compliance System FULLY IMPLEMENTED!")
        return True
    else:
        print("❌ Some tests failed. Implementation incomplete.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
