# services/search/embedding_service.py
from sentence_transformers import SentenceTransformer
import numpy as np
from typing import List, Dict, Optional, Tuple
import asyncio
import logging
from datetime import datetime
import hashlib
import json


class EmbeddingService:
    """Generate and manage vector embeddings for semantic search"""

    def __init__(self, session):
        self.session = session
        self.logger = logging.getLogger(__name__)

        # Load embedding models
        self.content_model = SentenceTransformer("all-MiniLM-L6-v2")  # 384 dim
        self.title_model = SentenceTransformer("all-MiniLM-L6-v2")  # 384 dim
        self.large_model = SentenceTransformer("all-mpnet-base-v2")  # 768 dim

        # Model metadata
        self.model_info = {
            "content_model": {"name": "all-MiniLM-L6-v2", "version": "1.0", "dimensions": 384},
            "large_model": {"name": "all-mpnet-base-v2", "version": "1.0", "dimensions": 768},
        }

    async def generate_page_embeddings(self, page_data: Dict) -> Dict:
        """Generate embeddings for page content, title, and summary"""
        try:
            page_id = page_data.get("page_id")
            content_text = page_data.get("content_text", "")
            title = page_data.get("title", "")
            summary = page_data.get("summary", "")

            if not content_text or len(content_text.strip()) < 50:
                return {"error": "Insufficient content for embedding generation"}

            # Prepare texts for embedding
            content_for_embedding = self._prepare_text_for_embedding(content_text, max_length=5000)
            title_for_embedding = self._prepare_text_for_embedding(title, max_length=500)
            summary_for_embedding = self._prepare_text_for_embedding(summary, max_length=1000)

            # Generate embeddings in parallel
            tasks = [
                self._generate_content_embedding(content_for_embedding),
                self._generate_title_embedding(title_for_embedding),
                self._generate_summary_embedding(summary_for_embedding),
            ]

            embeddings = await asyncio.gather(*tasks, return_exceptions=True)

            # Store embeddings in database
            embedding_data = {
                "page_id": page_id,
                "content_embedding": (
                    embeddings[0] if not isinstance(embeddings[0], Exception) else None
                ),
                "title_embedding": (
                    embeddings[1] if not isinstance(embeddings[1], Exception) else None
                ),
                "summary_embedding": (
                    embeddings[2] if not isinstance(embeddings[2], Exception) else None
                ),
                "content_length": len(content_text),
                "language": page_data.get("detected_language", "en"),
                "content_hash": hashlib.sha256(content_text.encode()).hexdigest(),
            }

            stored_embedding = await self._store_embeddings(embedding_data)

            return {
                "success": True,
                "embedding_id": stored_embedding["id"],
                "dimensions": {
                    "content": len(embeddings[0]) if embeddings[0] is not None else 0,
                    "title": len(embeddings[1]) if embeddings[1] is not None else 0,
                    "summary": len(embeddings[2]) if embeddings[2] is not None else 0,
                },
            }

        except Exception as e:
            self.logger.error(f"Embedding generation failed for page {page_id}: {e}")
            return {"error": str(e), "success": False}

    async def search_similar_content(
        self, query: str, limit: int = 10, similarity_threshold: float = 0.7
    ) -> List[Dict]:
        """Search for similar content using vector similarity (JSONB fallback)"""
        try:
            # Generate query embedding
            query_embedding = self.large_model.encode(query, convert_to_tensor=False)
            query_vector = query_embedding.tolist()

            # Perform vector similarity search using JSONB and cosine similarity
            from sqlalchemy import text
            import json

            # Convert query vector to JSON string for PostgreSQL
            query_vector_json = json.dumps(query_vector)

            # Use custom cosine similarity function for JSONB vectors
            # Using format string to avoid parameter binding issues
            similarity_query = text(f"""
                WITH vector_similarities AS (
                    SELECT
                        pv.page_id,
                        p.url,
                        p.title,
                        p.meta_description,
                        -- Calculate cosine similarity manually
                        (
                            SELECT
                                SUM(a.value::float * b.value::float) /
                                (SQRT(SUM(a.value::float * a.value::float)) * SQRT(SUM(b.value::float * b.value::float)))
                            FROM jsonb_array_elements(pv.content_embedding) WITH ORDINALITY a(value, idx)
                            JOIN jsonb_array_elements('{query_vector_json}'::jsonb) WITH ORDINALITY b(value, idx2)
                                ON a.idx = b.idx2
                        ) as similarity_score
                    FROM page_vectors pv
                    JOIN pages p ON pv.page_id = p.id
                    WHERE pv.content_embedding IS NOT NULL
                )
                SELECT page_id, url, title, meta_description, similarity_score
                FROM vector_similarities
                WHERE similarity_score > {similarity_threshold}
                ORDER BY similarity_score DESC
                LIMIT {limit}
            """)

            results = self.session.execute(similarity_query).fetchall()

            # Format and return results
            return [
                {
                    "page_id": str(result.page_id),
                    "url": result.url,
                    "title": result.title,
                    "description": result.meta_description,
                    "similarity_score": (
                        float(result.similarity_score) if result.similarity_score else 0.0
                    ),
                }
                for result in results
            ]

        except Exception as e:
            self.logger.error(f"Vector search failed: {e}")
            return []

    def _prepare_text_for_embedding(self, text: str, max_length: int) -> str:
        """Prepare text for embedding by cleaning and truncating."""
        # Simple cleaning: remove extra whitespace
        cleaned_text = " ".join(text.split())
        # Truncate to max_length
        return cleaned_text[:max_length]

    async def _generate_content_embedding(self, text: str) -> Optional[np.ndarray]:
        """Generate content embedding using the large model."""
        if not text:
            return None
        return self.large_model.encode(text, convert_to_tensor=False)

    async def _generate_title_embedding(self, text: str) -> Optional[np.ndarray]:
        """Generate title embedding using the large model."""
        if not text:
            return None
        return self.large_model.encode(text, convert_to_tensor=False)

    async def _generate_summary_embedding(self, text: str) -> Optional[np.ndarray]:
        """Generate summary embedding using the content model."""
        if not text:
            return None
        return self.content_model.encode(text, convert_to_tensor=False)

    async def _store_embeddings(self, embedding_data: Dict) -> Dict:
        """Store embeddings in the page_vectors table."""
        from models.page_vectors import PageVector

        try:
            # Convert embeddings to lists for JSONB storage
            content_embedding = (
                embedding_data['content_embedding'].tolist()
                if embedding_data['content_embedding'] is not None else None
            )
            title_embedding = (
                embedding_data['title_embedding'].tolist()
                if embedding_data['title_embedding'] is not None else None
            )
            summary_embedding = (
                embedding_data['summary_embedding'].tolist()
                if embedding_data['summary_embedding'] is not None else None
            )

            # Create new vector entry
            new_vector_entry = PageVector(
                page_id=embedding_data['page_id'],
                content_embedding=content_embedding,
                title_embedding=title_embedding,
                summary_embedding=summary_embedding,
                model_name=self.model_info['large_model']['name'],
                model_version=self.model_info['large_model']['version'],
                content_length=embedding_data['content_length'],
                language=embedding_data['language'],
                content_hash=embedding_data['content_hash']
            )

            self.session.add(new_vector_entry)
            self.session.commit()
            self.session.refresh(new_vector_entry)

            self.logger.info(f"Successfully stored embeddings for page_id: {embedding_data['page_id']}")
            return {'id': str(new_vector_entry.id)}

        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to store embeddings: {e}")
            raise e
