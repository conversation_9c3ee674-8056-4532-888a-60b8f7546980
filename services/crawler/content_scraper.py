import asyncio
import aiohttp
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

# Assuming AdaptiveRateLimiter and RobotsHandler will be imported
from .rate_limiter import AdaptiveRateLimiter # (Adjust import path as needed)
from .robots_handler import RobotsHandler # (Adjust import path as needed)

logger = logging.getLogger(__name__)

class EnhancedContentScraper:
    """Enterprise-grade content scraping with error handling and metadata extraction"""

    def __init__(self, session, rate_limiter, robots_handler, user_agent: Optional[str] = None):
        self.db_session = session # Assuming this is a SQLAlchemy session
        self.rate_limiter = rate_limiter
        self.robots_handler = robots_handler
        self.user_agent = user_agent or "UniversalWebDirectory/1.0 (+https://example.com/bot)"
        logger.info(f"EnhancedContentScraper initialized with User-Agent: {self.user_agent}")

    async def scrape_page(self, url: str) -> Dict:
        """Scrape a single page with comprehensive metadata extraction"""
        domain = urlparse(url).netloc
        request_start_time = datetime.utcnow() # For tracking total time including rate limiting

        # Check robots.txt compliance
        if not self.robots_handler.can_crawl(url):
            logger.warning(f"Scraping blocked by robots.txt for URL: {url}")
            # Record this attempt as a failure due to robots.txt
            self.rate_limiter.record_request_result(
                domain=domain,
                url=url,
                success=False,
                response_time_ms=0, # No actual request made
                status_code=None, # No HTTP status
                error_type="RobotsBlocked",
                error_message="URL blocked by robots.txt"
            )
            return {'error': 'Blocked by robots.txt', 'url': url, 'status_code': None}

        # Apply rate limiting
        # wait_if_needed returns delay in seconds, convert to ms for consistency if needed
        delay_seconds = await self.rate_limiter.wait_if_needed(domain)
        if delay_seconds > 0:
            logger.info(f"Rate limiter delayed request for {url} by {delay_seconds:.2f}s")

        response_data = {'url': url} # Initialize response data
        http_status = None
        response_time_ms = 0
        content_length = None
        is_success = False
        error_type_str = None
        error_message_str = None

        try:
            async with aiohttp.ClientSession(headers={'User-Agent': self.user_agent}) as client_session:
                actual_request_start_time = datetime.utcnow()
                async with client_session.get(
                    url,
                    timeout=aiohttp.ClientTimeout(total=30) # 30 seconds total timeout
                ) as response:
                    actual_request_end_time = datetime.utcnow()
                    response_time_ms = int((actual_request_end_time - actual_request_start_time).total_seconds() * 1000)

                    http_status = response.status
                    content = await response.text() # Read content
                    content_length = len(content) # Get content length from actual content read

                    if response.status == 200:
                        is_success = True
                        extracted_data = await self._extract_page_data(url, content, response)
                        response_data.update(extracted_data)
                    else:
                        is_success = False
                        error_type_str = f"HTTPError"
                        error_message_str = f"HTTP {response.status}"
                        response_data['error'] = error_message_str
                        response_data['status_code'] = response.status
                        logger.warning(f"HTTP {response.status} for {url}. Response time: {response_time_ms}ms")

        except aiohttp.ClientError as e: # More specific aiohttp errors
            is_success = False
            error_type_str = type(e).__name__
            error_message_str = str(e)
            response_data['error'] = error_message_str
            logger.error(f"AIOHTTP ClientError scraping {url}: {e}. Type: {error_type_str}")
        except asyncio.TimeoutError:
            is_success = False
            error_type_str = "TimeoutError"
            error_message_str = "Request timed out after 30 seconds"
            response_data['error'] = error_message_str
            logger.error(f"Timeout scraping {url}")
        except Exception as e:
            is_success = False
            error_type_str = type(e).__name__
            error_message_str = str(e)
            response_data['error'] = error_message_str
            logger.error(f"Generic error scraping {url}: {e}. Type: {error_type_str}")

        # Record request result using the rate limiter
        # This happens regardless of success or failure of the HTTP request itself
        # (unless it was blocked by robots.txt before any attempt)
        self.rate_limiter.record_request_result(
            domain=domain,
            url=url,
            success=is_success,
            response_time_ms=response_time_ms,
            status_code=http_status,
            content_length=content_length,
            error_type=error_type_str,
            error_message=error_message_str
        )

        return response_data

    async def _extract_page_data(self, url: str, content: str, response: aiohttp.ClientResponse) -> Dict:
        """Extract comprehensive page data and metadata"""
        soup = BeautifulSoup(content, 'html.parser') # Consider 'lxml' for performance

        # Basic metadata
        title_tag = soup.find('title')
        title_text = title_tag.get_text(strip=True) if title_tag else ''

        meta_description_tag = soup.find('meta', attrs={'name': lambda x: x and x.lower() == 'description'})
        description = meta_description_tag.get('content', '').strip() if meta_description_tag else ''

        # Content extraction
        for script_or_style in soup(["script", "style"]):
            script_or_style.decompose()

        text_content = soup.get_text(separator=' ', strip=True)

        # Extract headings
        h1_tags = [h.get_text(strip=True) for h in soup.find_all('h1')]
        h2_tags = [h.get_text(strip=True) for h in soup.find_all('h2')]
        h3_tags = [h.get_text(strip=True) for h in soup.find_all('h3')]

        # Extract links
        links = []
        base_domain = urlparse(url).netloc
        for link_tag in soup.find_all('a', href=True):
            href = link_tag['href']
            if not href or href.startswith('#') or href.startswith('javascript:'):
                continue

            absolute_url = urljoin(url, href) # Handles relative URLs
            anchor_text = link_tag.get_text(strip=True)

            is_internal = False
            try:
                link_domain = urlparse(absolute_url).netloc
                if link_domain == base_domain:
                    is_internal = True
            except ValueError: # Handle potential invalid URLs from urljoin
                logger.debug(f"Could not parse URL for link: {absolute_url} on page {url}")


            links.append({
                'url': absolute_url,
                'anchor_text': anchor_text,
                'is_internal': is_internal,
                'nofollow': 'rel' in link_tag.attrs and 'nofollow' in link_tag['rel']
            })

        internal_links_count = sum(1 for link in links if link['is_internal'])
        external_links_count = len(links) - internal_links_count
        image_count = len(soup.find_all('img'))

        content_hash = hashlib.sha256(text_content.encode('utf-8')).hexdigest()

        return {
            'url': url,
            'title': title_text,
            'meta_description': description,
            'content_text': text_content,
            'content_html': str(soup), # Full HTML after removing script/style
            'content_length': len(content), # Original content length
            'word_count': len(text_content.split()),
            'heading_h1': h1_tags,
            'heading_h2': h2_tags,
            'heading_h3': h3_tags,
            'internal_links_count': internal_links_count,
            'external_links_count': external_links_count,
            'image_count': image_count,
            'links': links, # List of link dicts
            'content_hash': content_hash,
            'http_status': response.status,
            'content_type': response.headers.get('Content-Type', ''),
            'last_modified': response.headers.get('Last-Modified'), # Keep as string
            'etag': response.headers.get('ETag'),
            'scraped_at': datetime.utcnow().isoformat() # ISO format string
        }

# Note: For testing this service, use the test files in tests/ directory
# This service should be imported and used by other components, not run directly
