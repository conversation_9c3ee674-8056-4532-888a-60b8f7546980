#!/usr/bin/env python3
"""
Content Scraping Service - Migrated from core/content_scraper.py
Part of the new services architecture for Milestone 0.4
"""

import logging
from typing import List, Optional, Dict, Any
from core.content_scraper import ContentScraper

logger = logging.getLogger(__name__)

class ContentScrapingService:
    """
    Service wrapper for Content Scraping functionality.
    Migrated from core/content_scraper.py with enhanced error handling and configuration.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the Content Scraping Service"""
        self.config = config or {}
        self.scraper = ContentScraper()
        logger.info("ContentScrapingService initialized")

    def scrape_urls(self, urls: List[str]) -> Dict[str, Any]:
        """
        Scrape multiple URLs and save to database

        Args:
            urls: List of URLs to scrape

        Returns:
            Dict with results summary
        """
        results = {
            'scraped': 0,
            'failed': 0,
            'errors': [],
            'page_ids': []
        }

        for url in urls:
            try:
                page_id = self.scraper.scrape_and_save(url)
                if page_id:
                    results['scraped'] += 1
                    results['page_ids'].append(page_id)
                else:
                    results['failed'] += 1
            except Exception as e:
                results['failed'] += 1
                results['errors'].append(f"Error scraping {url}: {str(e)}")
                logger.error(f"Failed to scrape URL {url}: {e}")

        return results

    def scrape_single_url(self, url: str) -> Optional[str]:
        """
        Scrape a single URL and save to database

        Args:
            url: URL to scrape

        Returns:
            Page ID if successful, None if failed
        """
        try:
            return self.scraper.scrape_and_save(url)
        except Exception as e:
            logger.error(f"Error scraping {url}: {e}")
            return None

    def extract_content(self, url: str, html_content: str) -> Dict[str, Any]:
        """
        Extract structured content from HTML

        Args:
            url: Source URL
            html_content: Raw HTML content

        Returns:
            Extracted content dictionary
        """
        try:
            return self.scraper.extract_content(html_content, url)
        except Exception as e:
            logger.error(f"Error extracting content from {url}: {e}")
            return {}
