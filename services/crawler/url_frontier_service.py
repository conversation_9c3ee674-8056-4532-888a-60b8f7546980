# services/crawler/url_frontier_service.py - Advanced URL Frontier Service (Milestone 1.1)
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text
from config.database import SessionLocal
from models.url_frontier import URLFrontier

logger = logging.getLogger(__name__)

class URLFrontierService:
    """Enterprise-grade URL frontier service with distributed crawling support"""
    
    def __init__(self, session: Optional[Session] = None):
        self.session = session or SessionLocal()
        self._should_close_session = session is None
        logger.info("URLFrontierService initialized")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._should_close_session:
            self.session.close()

    def add_url(self, url: str, priority: int = 5, source_type: str = 'discovered') -> bool:
        """Add URL to frontier with deduplication"""
        try:
            url_entry = URLFrontier(url=url, priority=priority, source_type=source_type)
            self.session.add(url_entry)
            self.session.commit()
            logger.info(f"Added URL to frontier: {url} (priority={priority}, source={source_type})")
            return True
        except Exception as e:
            self.session.rollback()
            logger.warning(f"Failed to add URL {url}: {e}")
            return False

    def add_urls_batch(self, urls: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Add multiple URLs in batch with enhanced error handling"""
        added = 0
        failed = 0
        errors = []
        
        for url_data in urls:
            url = url_data.get('url')
            priority = url_data.get('priority', 5)
            source_type = url_data.get('source_type', 'discovered')
            
            if self.add_url(url, priority, source_type):
                added += 1
            else:
                failed += 1
                errors.append(url)
        
        result = {
            'added': added,
            'failed': failed,
            'errors': errors,
            'total': len(urls)
        }
        
        logger.info(f"Batch URL addition: {added} added, {failed} failed out of {len(urls)} total")
        return result

    def get_next_urls(self, limit: int = 10, worker_id: str = None) -> List[URLFrontier]:
        """Get next URLs for crawling with distributed locking"""
        try:
            # Use raw SQL for atomic update with SKIP LOCKED
            query = text("""
                UPDATE url_frontier
                SET status = 'crawling',
                    updated_at = NOW(),
                    worker_id = :worker_id,
                    crawl_attempts = crawl_attempts + 1
                WHERE id IN (
                    SELECT id FROM url_frontier
                    WHERE status = 'pending'
                    AND scheduled_for <= NOW()
                    ORDER BY priority DESC, scheduled_for ASC
                    LIMIT :limit
                    FOR UPDATE SKIP LOCKED
                )
                RETURNING id, url, priority, crawl_attempts, domain, path;
            """)
            
            result = self.session.execute(query, {
                'worker_id': worker_id or f'worker_{datetime.now().timestamp()}',
                'limit': limit
            })
            
            # Fetch the updated URLs
            url_ids = [row.id for row in result]
            urls = self.session.query(URLFrontier).filter(URLFrontier.id.in_(url_ids)).all()
            
            self.session.commit()
            logger.info(f"Retrieved {len(urls)} URLs for crawling (worker: {worker_id})")
            return urls
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to get next URLs: {e}")
            return []

    def mark_crawled(self, url_id: str, success: bool = True, error_msg: str = None, 
                    http_status: int = None, content_type: str = None, 
                    content_length: int = None, etag: str = None) -> bool:
        """Mark URL as crawled or failed with comprehensive metadata"""
        try:
            url_entry = self.session.query(URLFrontier).filter_by(id=url_id).first()
            if not url_entry:
                logger.warning(f"URL entry not found: {url_id}")
                return False
            
            if success:
                url_entry.status = 'crawled'
                url_entry.last_crawled = datetime.utcnow()
                url_entry.next_crawl = datetime.utcnow() + timedelta(days=7)  # Recrawl weekly
                url_entry.worker_id = None
                url_entry.error_message = None
                url_entry.error_type = None
                
                # Update metadata
                if content_type:
                    url_entry.content_type = content_type
                if content_length:
                    url_entry.content_length = content_length
                if etag:
                    url_entry.etag = etag
                if http_status:
                    url_entry.http_status = http_status
                    
                logger.info(f"Marked URL as crawled: {url_entry.url}")
            else:
                url_entry.crawl_attempts += 1
                if url_entry.crawl_attempts >= url_entry.max_attempts:
                    url_entry.status = 'error'
                    url_entry.worker_id = None
                    logger.warning(f"URL marked as error after {url_entry.crawl_attempts} attempts: {url_entry.url}")
                else:
                    url_entry.status = 'pending'
                    url_entry.scheduled_for = datetime.utcnow() + timedelta(hours=1)  # Retry in 1 hour
                    url_entry.worker_id = None
                    logger.info(f"URL scheduled for retry: {url_entry.url}")
                
                url_entry.error_message = error_msg
                url_entry.http_status = http_status
                
                # Set error type based on HTTP status
                if http_status:
                    if 400 <= http_status < 500:
                        url_entry.error_type = 'client_error'
                    elif 500 <= http_status < 600:
                        url_entry.error_type = 'server_error'
                    else:
                        url_entry.error_type = 'http_error'
                else:
                    url_entry.error_type = 'network_error'
            
            self.session.commit()
            return True
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to mark URL as crawled: {e}")
            return False

    def get_frontier_stats(self) -> Dict[str, Any]:
        """Get comprehensive frontier statistics"""
        try:
            stats = {}
            
            # Status distribution
            status_query = text("""
                SELECT status, COUNT(*) as count
                FROM url_frontier
                GROUP BY status
                ORDER BY count DESC;
            """)
            status_result = self.session.execute(status_query)
            stats['status_distribution'] = {row.status: row.count for row in status_result}
            
            # Priority distribution
            priority_query = text("""
                SELECT priority, COUNT(*) as count
                FROM url_frontier
                GROUP BY priority
                ORDER BY priority DESC;
            """)
            priority_result = self.session.execute(priority_query)
            stats['priority_distribution'] = {row.priority: row.count for row in priority_result}
            
            # Domain distribution (top 10)
            domain_query = text("""
                SELECT domain, COUNT(*) as count
                FROM url_frontier
                GROUP BY domain
                ORDER BY count DESC
                LIMIT 10;
            """)
            domain_result = self.session.execute(domain_query)
            stats['top_domains'] = {row.domain: row.count for row in domain_result}
            
            # Source type distribution
            source_query = text("""
                SELECT source_type, COUNT(*) as count
                FROM url_frontier
                WHERE source_type IS NOT NULL
                GROUP BY source_type
                ORDER BY count DESC;
            """)
            source_result = self.session.execute(source_query)
            stats['source_distribution'] = {row.source_type: row.count for row in source_result}
            
            # Total counts
            total_query = text("SELECT COUNT(*) as total FROM url_frontier;")
            total_result = self.session.execute(total_query)
            stats['total_urls'] = total_result.scalar()
            
            # Pending URLs ready for crawling
            pending_query = text("""
                SELECT COUNT(*) as pending
                FROM url_frontier
                WHERE status = 'pending' AND scheduled_for <= NOW();
            """)
            pending_result = self.session.execute(pending_query)
            stats['ready_for_crawl'] = pending_result.scalar()
            
            logger.info(f"Generated frontier statistics: {stats['total_urls']} total URLs")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get frontier stats: {e}")
            return {}

    def cleanup_stale_workers(self, timeout_hours: int = 2) -> int:
        """Clean up URLs stuck in 'crawling' status from dead workers"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=timeout_hours)
            
            query = text("""
                UPDATE url_frontier
                SET status = 'pending',
                    worker_id = NULL,
                    scheduled_for = NOW() + INTERVAL '1 hour'
                WHERE status = 'crawling'
                AND updated_at < :cutoff_time
                RETURNING id;
            """)
            
            result = self.session.execute(query, {'cutoff_time': cutoff_time})
            cleaned_count = len(result.fetchall())
            self.session.commit()
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} stale worker URLs")
            
            return cleaned_count
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to cleanup stale workers: {e}")
            return 0

    def reset_failed_urls(self, max_age_days: int = 7) -> int:
        """Reset old failed URLs for retry"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=max_age_days)
            
            query = text("""
                UPDATE url_frontier
                SET status = 'pending',
                    crawl_attempts = 0,
                    error_message = NULL,
                    error_type = NULL,
                    scheduled_for = NOW()
                WHERE status = 'error'
                AND updated_at < :cutoff_time
                RETURNING id;
            """)
            
            result = self.session.execute(query, {'cutoff_time': cutoff_time})
            reset_count = len(result.fetchall())
            self.session.commit()
            
            if reset_count > 0:
                logger.info(f"Reset {reset_count} failed URLs for retry")
            
            return reset_count
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to reset failed URLs: {e}")
            return 0
