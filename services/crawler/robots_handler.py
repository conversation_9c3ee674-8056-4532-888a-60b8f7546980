# This file will contain the service for fetching, parsing, and respecting
# robots.txt files for websites.




import urllib.robotparser
import requests
from datetime import datetime, timedelta
from config.database import get_db_session
from models.robots_cache import RobotsCache

# Placeholder class



class RobotsHandlerService:
    def __init__(self, cache_duration_seconds=86400):
        self.cache_duration = cache_duration_seconds
        self.user_agent = "*"

    def _fetch_and_parse_robots(self, domain):
        """
        Fetch robots.txt from the domain, parse it, and return the content and rules.
        """
        robots_url = f"http://{domain}/robots.txt"
        try:
            response = requests.get(robots_url, timeout=5)
            response.raise_for_status()
            robots_txt = response.text
            parser = urllib.robotparser.RobotFileParser()
            parser.parse(robots_txt.splitlines())
            # Parse rules for storage (basic example, can be extended)
            rules = self._extract_rules(robots_txt)
            user_agent_rules = self._extract_user_agent_rules(robots_txt)
            crawl_delay = parser.crawl_delay(self.user_agent)
            sitemaps = parser.site_maps() if hasattr(parser, 'site_maps') else []
            is_accessible = True
            last_error = None
        except Exception as e:
            robots_txt = None
            rules = None
            user_agent_rules = None
            crawl_delay = None
            sitemaps = []
            is_accessible = False
            last_error = str(e)
        return {
            'robots_txt': robots_txt,
            'parsed_rules': rules,
            'user_agent_rules': user_agent_rules,
            'crawl_delay': crawl_delay,
            'sitemap_urls': sitemaps,
            'is_accessible': is_accessible,
            'last_error': last_error
        }

    def _extract_rules(self, robots_txt):
        # TODO: Implement advanced robots.txt parsing into structured rules (JSON)
        return {'raw': robots_txt}

    def _extract_user_agent_rules(self, robots_txt):
        # TODO: Parse user-agent specific rules into structured JSON
        return {'*': robots_txt}

    def _get_db_cache(self, db, domain: str):
        return db.query(RobotsCache).filter_by(domain=domain).first()

    def _is_cache_valid(self, cache):
        if not cache:
            return False
        if cache.expires_at and cache.expires_at > datetime.utcnow():
            return True
        return False

    def _update_db_cache(self, db, domain: str, data: dict):
        expires_at = datetime.utcnow() + timedelta(seconds=self.cache_duration)
        cache = db.query(RobotsCache).filter_by(domain=domain).first()
        if not cache:
            cache = RobotsCache(domain=domain)
            db.add(cache)
        cache.robots_txt = data['robots_txt']
        cache.parsed_rules = data['parsed_rules']
        cache.user_agent_rules = data['user_agent_rules']
        cache.crawl_delay = data['crawl_delay']
        cache.sitemap_urls = data['sitemap_urls']
        cache.fetched_at = datetime.utcnow()
        cache.expires_at = expires_at
        cache.last_error = data['last_error']
        cache.is_accessible = data['is_accessible']
        cache.fetch_attempts = (cache.fetch_attempts or 0) + 1
        db.commit()
        return cache

    def get_robots_policy(self, domain: str):
        """
        Get robots.txt policy for a domain, using DB cache and updating if expired.
        Returns a RobotsCache instance with rules, crawl_delay, sitemaps, etc.
        """
        with next(get_db_session()) as db:
            cache = self._get_db_cache(db, domain)
            if self._is_cache_valid(cache):
                return cache
            # Fetch and update cache
            data = self._fetch_and_parse_robots(domain)
            cache = self._update_db_cache(db, domain, data)
            return cache

    def can_fetch(self, user_agent, url):
        """
        Checks if the given user_agent is allowed to fetch the given URL
        based on the website's robots.txt (DB-backed).
        """
        import urllib.parse
        parsed_url = urllib.parse.urlparse(url)
        domain = parsed_url.netloc
        if not domain:
            return False
        cache = self.get_robots_policy(domain)
        # Basic logic: if not accessible, disallow
        if not cache or not cache.is_accessible:
            return False
        # TODO: Implement full rule parsing and matching for user_agent and path
        # For now, allow if robots.txt is accessible
        return True

    def get_crawl_delay(self, user_agent, domain):
        cache = self.get_robots_policy(domain)
        if cache:
            return cache.crawl_delay
        return None

    def get_sitemap_urls(self, domain):
        cache = self.get_robots_policy(domain)
        if cache and cache.sitemap_urls:
            return cache.sitemap_urls
        return []


