import requests
import json
from bs4 import BeautifulSoup

# Create a list of URLs to scrape

urls = [
'https://www.example.com/',
'https://www.example2.com/',
'https://www.example3.com/'
]

# Create an empty list to store the scraped data

data = []

# Loop through each URL

for url in urls:

# Make a GET request to fetch the raw HTML content

html_content = requests.get(url).text


# Parse the html content

soup = BeautifulSoup(html_content, "lxml")

# Scrape the desired data

title = soup.find("title").text
body = soup.find("body").text

# Store the scraped data in a dictionary

scraped_data = {
    'title': title,
    'body': body
}

# Add the dictionary to the list

data.append(scraped_data)
# Write the list of scraped data to a JSON file

with open('data.json', 'w') as f:
json.dump(data, f)
