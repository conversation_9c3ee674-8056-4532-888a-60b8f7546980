To build a general AI model trained on the JSON files sored locally on an ubuntu server follow these steps:

First, ensure that you have the necessary libraries installed, such as requests, BeautifulSoup, and json.

Next, set the directory path where the text files containing the URLs are located.

Create an empty list to store the URLs.

Loop through each file in the directory, checking if the file is a text file and adding each line of the file to the list of URLs.

Remove any leading or trailing whitespace from the URLs in the list.

Create an empty list to store the scraped data.

Loop through each URL in the list and make a GET request to fetch the raw HTML content.

Parse the HTML content using BeautifulSoup and scrape the desired data, such as the title and body of the page.

Store the scraped data in a dictionary and add the dictionary to the list of scraped data.

Write the list of scraped data to a JSON file using the json library.

Loop through each file in the directory again and delete the text files.

Once the JSON files are created, you can use them to train a general AI model. This can be done by loading the JSON data into a machine learning library, such as TensorFlow or PyTorch, and using it to train a model on the scraped data.

The trained model can then be used to make predictions on new data, such as predicting the title and body of a new webpage given its URL.

import os
from bs4 import BeautifulSoup
import requests
import json

dir_path = '/path/to/directory'

# Create an empty list to store the URLs

urls = []

# Loop through each file in the directory and add each line of the file to the list of URLs

for file in os.listdir(dir_path):
if file.endswith(".txt"):
with open(os.path.join(dir_path, file), 'r') as f:
urls.extend(f.readlines())

# Remove any leading or trailing whitespace from the URLs in the list

urls = [url.strip() for url in urls]

# Create an empty list to store the scraped data

data = []

# Loop through each URL in the list and make a GET request to fetch the raw HTML content

for url in urls:
response = requests.get(url)
html = response.text

# Parse the HTML content using BeautifulSoup and scrape the desired data

soup = BeautifulSoup(html, 'html.parser')
title = soup.find('title').text
body = soup.find('body').text

# Store the scraped data in a dictionary and add the dictionary to the list of scraped data

data.append({'title': title, 'body': body})

# Write the list of scraped data to a JSON file

with open('data.json', 'w') as f:
json.dump(data, f)

# Loop through each file in the directory again and delete the text files

for file in os.listdir(dir_path):
if file.endswith(".txt"):
os.remove(os.path.join(dir_path, file))
