# Web Crawler

A modular Python web crawler for discovering, collecting, and processing website content for further analysis or AI model building.

## Features
- Discovers URLs from web pages and saves them for processing
- Scrapes web page content (title, body) from collected URLs
- Outputs structured data in JSON format
- Modular workflow: URL discovery and content scraping are separate steps
- Designed for extensibility and further feature development

## Directory Structure
```
web_crawler/
├── README.md
├── web_crawler_url_collector.py         # URL discovery script
├── web_crawler_ai_model_builder. -  v  0.3.py  # Content scraping script
├── documents/                          # Design notes and concepts
├── legacy_files/                       # Previous versions and notes
```

## Requirements
- Python 3.8+
- requests
- beautifulsoup4
- lxml

## Installation & Setup
1. Clone this repository.
2. (Recommended) Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate
   ```
3. Install dependencies:
   ```bash
   pip install requests beautifulsoup4 lxml
   ```

## Usage
### 1. URL Discovery
Run the URL collector to crawl target pages and save discovered links:
```bash
python web_crawler_url_collector.py
```
This will generate a timestamped `.txt` file with URLs.

### 2. Content Scraping
Edit the `dir_path` in the content scraper script to point to your URL files directory, then run:
```bash
python web_crawler_ai_model_builder. -  v  0.3.py
```
This will read the URL files, scrape content, and output `data.json`.

## Configuration
- Edit script variables (e.g., `dir_path`) to match your directory structure.
- For advanced configuration, consider adding a config file (see roadmap).

## Development Roadmap
- Add robust error handling and logging
- Support for recursive crawling and robots.txt
- URL validation and deduplication
- Configurable targets and output formats
- Unit tests and documentation improvements

## License
Specify your license here (e.g., MIT, Apache 2.0, etc.)

## Contributing
Pull requests and suggestions are welcome! For major changes, please open an issue first to discuss what you would like to change.

---
