# Universal Web Directory - Web Crawler

A next-generation web intelligence infrastructure designed to create a comprehensive, queryable database of the entire web's structure, content, and behavioral patterns. This project represents a paradigm shift from single-purpose web crawling to comprehensive web intelligence infrastructure.

## 🎯 Project Vision

The Universal Web Directory operates on the principle of **comprehensive data collection with application-agnostic querying**. Rather than building isolated crawlers for specific applications, this system creates a centralized platform where:

- **Data collection happens once** but serves infinite use cases
- **Applications become intelligent consumers** rather than data collectors
- **Cross-application insights emerge** from shared dataset analysis
- **Infrastructure costs scale efficiently** across multiple consumers

## 🚀 Current Status

**Development Stage**: Early Prototype (≈2% complete)

The project currently includes basic web crawling and content scraping capabilities. This represents the foundational layer of what will become a comprehensive web intelligence platform.

## 📁 Project Structure

```
web_crawler/
├── README.md                           # This file
├── requirements.txt                    # Python dependencies
├── url_discovery.py                    # URL discovery and collection
├── content_scraper.py                  # Content extraction and processing
├── documents/                          # Design specifications
│   └── Universal Web Directory Features overall concept.txt
├── legacy_files/                       # Development history
│   ├── internet scraper. v - 0.01.txt
│   ├── web crawler ai model - v 0.01.txt
│   ├── web crawler ai model - v 0.02.txt
│   ├── web crawler ai model - v 0.03.txt
│   ├── web_crawler_ai_model_builder. -  v  0.1.txt
│   └── web_crawler_ai_model_builder. -  v  0.2.txt
└── web_crawler.code-workspace          # VS Code workspace
```

## 🛠️ Current Components

### 1. URL Discovery (`url_discovery.py`)
- **Purpose**: Discovers and collects URLs from target websites
- **Current Capability**: Basic link extraction from web pages
- **Output**: Timestamped text files containing discovered URLs
- **Status**: ⚠️ Needs enhancement (currently hardcoded to example.com)

### 2. Content Scraper (`content_scraper.py`)
- **Purpose**: Extracts content from discovered URLs
- **Current Capability**: Basic title and body text extraction
- **Output**: JSON files with structured content data
- **Status**: ⚠️ Needs robust error handling and validation

## 🔧 Technical Requirements

### Environment Setup
- **Python**: 3.12+ (recommended)
- **Environment Manager**: Anaconda/Conda
- **Dependencies**: Managed via `requirements.txt`

### Installation

1. **Create Conda Environment**:
   ```bash
   conda create --name web_crawler python=3.12 -y
   conda activate web_crawler
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Installation**:
   ```bash
   python -c "import requests, bs4; print('Dependencies installed successfully')"
   ```

### Dependencies Overview

The project uses a comprehensive `requirements.txt` file with:

- **Core Dependencies**: `requests`, `beautifulsoup4`, `lxml` for web scraping
- **Enhancement Tools**: `pydantic`, `loguru`, `ratelimit` for robust functionality
- **Development Tools**: `black`, `flake8`, `pytest` for code quality
- **Future Capabilities**: Commented sections for NLP, databases, and advanced crawling

For development with additional features, uncomment desired packages in `requirements.txt` and reinstall.

## 🚦 Current Usage

### Step 1: URL Discovery
```bash
conda activate web_crawler
python url_discovery.py
```
**Output**: Creates timestamped `.txt` files with discovered URLs

### Step 2: Content Scraping
```bash
# Ensure url_files directory exists and contains URL files
mkdir -p url_files
python content_scraper.py
```
**Output**: Creates `scraped_data.json` with extracted content

## ⚠️ Known Issues & Limitations

### Critical Issues
- [ ] Hardcoded target URLs (example.com placeholder)
- [ ] No error handling for network failures
- [ ] Directory structure assumptions not validated
- [ ] File deletion without backup in content scraper
- [ ] Workflow coordination between URL discovery and content scraping

### Major Limitations
- [ ] No rate limiting or politeness delays
- [ ] No robots.txt compliance
- [ ] No URL validation or deduplication
- [ ] Basic content extraction only (title/body)
- [ ] No configuration management system

### Code Quality Issues
- [ ] Missing docstrings and type hints
- [ ] Broad exception handling
- [ ] Import order and linting issues
- [ ] No logging framework implementation

## 🗺️ Development Roadmap

### Phase 0: Environment & Architecture (✅ COMPLETED)
- [x] PostgreSQL database setup with proper configuration
- [x] All 126 directory websites loaded as seed URLs
- [x] New modular project structure implemented
- [x] Legacy code successfully migrated to services architecture
- [x] Basic tests passing
- [x] Documentation updated

### Phase 1: Foundation (Current Priority)
- [x] Create comprehensive requirements.txt file
- [x] Update project documentation
- [x] Install dependencies in conda environment
- [x] Create proper directory structure
- [x] Add configuration management
- [x] Implement basic logging
- [ ] Implement robust error handling

### Phase 2: Core Functionality
- [ ] URL validation and filtering
- [ ] Rate limiting and politeness
- [ ] Robots.txt compliance
- [ ] Enhanced content extraction
- [ ] Data deduplication

### Phase 3: Intelligence Layer
- [ ] Content classification and tagging
- [ ] NLP-based content analysis
- [ ] Metadata extraction and enrichment
- [ ] Quality scoring algorithms
- [ ] Relationship mapping

### Phase 4: Advanced Features
- [ ] Vector-based semantic search
- [ ] Real-time trend detection
- [ ] Authority scoring systems
- [ ] Predictive analytics
- [ ] API development

### Phase 5: Scale & Production
- [ ] Distributed crawling architecture
- [ ] Database integration (hybrid vector/relational)
- [ ] Performance optimization
- [ ] Monitoring and alerting
- [ ] Production deployment

## 🎯 Long-term Vision

The Universal Web Directory aims to become a comprehensive web intelligence platform supporting:

- **E-commerce Intelligence**: Product research, competitor analysis
- **Academic Research**: Source discovery, citation networks
- **Marketing & PR**: Influencer identification, content placement
- **Business Intelligence**: Market research, trend analysis
- **Content Strategy**: Gap analysis, competitive insights
- **News Aggregation**: Source diversity, authority scoring

## 🤝 Contributing

This project is in active development. Contributions are welcome!

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Set up the conda environment: `conda activate web_crawler`
4. Install dependencies: `pip install -r requirements.txt`
5. Make your changes
6. Test thoroughly
7. Submit a pull request

### Development Guidelines
- Use the `web_crawler` conda environment for all development
- Install dependencies via `requirements.txt`
- Follow PEP 8 style guidelines (use `black` and `flake8`)
- Add comprehensive error handling
- Include docstrings and type hints
- Test changes before submitting (use `pytest`)
- Update documentation for new features

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For questions, issues, or suggestions:
- Open an issue on GitHub
- Review the design documents in `/documents/`
- Check the development history in `/legacy_files/`

---

**Note**: This project represents an ambitious vision for web intelligence infrastructure. The current implementation is a foundational prototype that will evolve significantly as development progresses.
