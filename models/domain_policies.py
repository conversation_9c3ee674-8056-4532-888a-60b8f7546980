import uuid
from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from models.base import Base

class DomainPolicy(Base):
    __tablename__ = 'domain_policies'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain = Column(String(255), unique=True, nullable=False)

    # Crawling policies
    is_allowed = Column(Boolean, default=True)
    crawl_delay = Column(Integer, default=1)  # Seconds between requests
    max_concurrent_requests = Column(Integer, default=1)
    respect_robots_txt = Column(Boolean, default=True)

    # Rate limiting
    requests_per_minute = Column(Integer, default=60)
    requests_per_hour = Column(Integer, default=3600)
    requests_per_day = Column(Integer, default=86400)

    # Quality metrics
    success_rate = Column(Float, default=1.0)
    avg_response_time = Column(Integer)  # Milliseconds
    last_successful_crawl = Column(DateTime)
    consecutive_failures = Column(Integer, default=0)

    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<DomainPolicy(domain='{self.domain}', is_allowed='{self.is_allowed}')>"
