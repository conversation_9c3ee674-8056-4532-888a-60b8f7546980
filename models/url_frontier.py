# models/url_frontier.py
from sqlalchemy import Column, String, Text, Integer, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from .base import Base

class URLFrontier(Base):
    """Model for URL crawling queue"""
    __tablename__ = 'url_frontier'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    url = Column(Text, nullable=False, index=True)
    site_id = Column(UUID(as_uuid=True), ForeignKey('sites.id', ondelete='CASCADE'), index=True)
    priority = Column(Integer, default=5, index=True)
    depth = Column(Integer, default=0)
    discovered_at = Column(DateTime, default=func.now(), nullable=False)
    scheduled_for = Column(DateTime, default=func.now(), index=True)
    attempts = Column(Integer, default=0)
    max_attempts = Column(Integer, default=3)
    status = Column(String(20), default='pending', index=True)  # pending, processing, completed, failed
    source_type = Column(String(50)) # Added as per Milestone 0.2
    error_message = Column(Text)
    source_url = Column(Text)
    source_page_id = Column(UUID(as_uuid=True), ForeignKey('pages.id', ondelete='SET NULL'))
    
    # Relationships
    site = relationship("Site", back_populates="url_frontier")
    source_page = relationship("Page", foreign_keys=[source_page_id])
    
    def __repr__(self):
        return f"<URLFrontier(url='{self.url}', status='{self.status}', priority={self.priority})>"
    
    def mark_processing(self):
        """Mark URL as being processed"""
        self.status = 'processing'
        self.attempts += 1
    
    def mark_completed(self):
        """Mark URL as successfully crawled"""
        self.status = 'completed'
    
    def mark_failed(self, error_message=None):
        """Mark URL as failed to crawl"""
        self.status = 'failed'
        if error_message:
            self.error_message = error_message
    
    def should_retry(self):
        """Check if URL should be retried"""
        return self.attempts < self.max_attempts and self.status in ['pending', 'failed']
