# models/url_frontier.py - Advanced URL Frontier System (Milestone 1.1)
from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, BIGINT
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
import hashlib
import uuid
from urllib.parse import urlparse, parse_qs
import json
from datetime import timed<PERSON><PERSON>

from .base import Base

class URLFrontier(Base):
    """Advanced URL frontier model for enterprise-grade web crawling"""
    __tablename__ = 'url_frontier'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    url = Column(String, unique=True, nullable=False)
    url_hash = Column(String(64), unique=True, nullable=False)
    domain = Column(String(255), nullable=False)
    subdomain = Column(String(255))
    path = Column(Text)
    query_params = Column(JSONB)

    # Crawling metadata
    status = Column(String(20), default='pending')  # pending, crawling, crawled, error, blocked
    priority = Column(Integer, default=5)  # 1-10 scale, 10 = highest
    source_type = Column(String(50))  # seed, discovered, sitemap, robots
    discovered_at = Column(DateTime, default=func.now())
    scheduled_for = Column(DateTime, default=func.now())
    last_crawled = Column(DateTime)
    next_crawl = Column(DateTime)
    crawl_attempts = Column(Integer, default=0)
    max_attempts = Column(Integer, default=3)

    # Error handling
    error_message = Column(Text)
    error_type = Column(String(50))
    http_status = Column(Integer)

    # Metadata
    content_type = Column(String(100))
    content_length = Column(BIGINT)
    last_modified = Column(DateTime)
    etag = Column(String(255))

    # Worker coordination
    worker_id = Column(String(100))

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __init__(self, url: str, **kwargs):
        """Initialize URL frontier entry with automatic URL parsing"""
        self.url = url
        self.url_hash = hashlib.sha256(url.encode()).hexdigest()
        parsed = urlparse(url)
        self.domain = parsed.netloc
        self.subdomain = parsed.hostname
        self.path = parsed.path
        if parsed.query:
            self.query_params = parse_qs(parsed.query)
        super().__init__(**kwargs)

    def __repr__(self):
        return f"<URLFrontier(url='{self.url}', status='{self.status}', priority={self.priority})>"

    def mark_crawling(self, worker_id: str = None):
        """Mark URL as being crawled"""
        self.status = 'crawling'
        self.crawl_attempts += 1
        self.worker_id = worker_id

    def mark_crawled(self, success: bool = True, error_msg: str = None, http_status: int = None):
        """Mark URL as crawled (success or failure)"""
        if success:
            self.status = 'crawled'
            self.last_crawled = func.now()
            # Schedule for recrawl in 7 days
            self.next_crawl = func.now() + timedelta(days=7)
            self.worker_id = None
        else:
            if self.crawl_attempts >= self.max_attempts:
                self.status = 'error'
                self.worker_id = None
            else:
                self.status = 'pending'
                # Retry in 1 hour
                self.scheduled_for = func.now() + timedelta(hours=1)
                self.worker_id = None

            self.error_message = error_msg
            self.http_status = http_status

    def should_retry(self):
        """Check if URL should be retried"""
        return self.crawl_attempts < self.max_attempts and self.status in ['pending', 'error']

    def is_due_for_crawl(self):
        """Check if URL is due for crawling"""
        from datetime import datetime
        now = datetime.utcnow()
        return (self.status == 'pending' and
                (self.scheduled_for is None or self.scheduled_for <= now)) or \
               (self.status == 'crawled' and
                self.next_crawl is not None and self.next_crawl <= now)
