# models/site.py
from sqlalchemy import Column, String, Text, <PERSON><PERSON><PERSON>, Integer, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from .base import Base, TimestampMixin

class Site(Base, TimestampMixin):
    """Model for websites/domains"""
    __tablename__ = 'sites'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain = Column(String(255), nullable=False, unique=True, index=True)
    title = Column(Text)
    description = Column(Text)
    language = Column(String(10), default='en')
    robots_txt = Column(Text)
    robots_last_fetched = Column(DateTime)
    crawl_delay = Column(Integer, default=1)
    last_crawled = Column(DateTime)
    is_active = Column(Boolean, default=True, index=True)
    
    # Relationships
    pages = relationship("Page", back_populates="site", cascade="all, delete-orphan")
    url_frontier = relationship("URLFrontier", back_populates="site", cascade="all, delete-orphan")
    crawl_logs = relationship("CrawlLog", back_populates="site", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Site(domain='{self.domain}', title='{self.title}')>"
    
    @property
    def base_url(self):
        """Get the base URL for this site"""
        return f"https://{self.domain}"
    
    def is_crawl_allowed(self, user_agent='*'):
        """Check if crawling is allowed based on robots.txt"""
        # TODO: Implement robots.txt parsing
        return True
