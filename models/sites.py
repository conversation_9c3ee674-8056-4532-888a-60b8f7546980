from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, Float, ARRAY, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB, INET
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from models.base import Base

class Site(Base):
    __tablename__ = 'sites'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain = Column(String(255), unique=True, nullable=False)
    subdomain = Column(String(255))

    # Site metadata
    title = Column(Text)
    description = Column(Text)
    language = Column(String(10))
    country = Column(String(3))

    # Technical information
    ip_address = Column(INET)
    server_software = Column(String(100))
    cms_platform = Column(String(100))
    technology_stack = Column(JSONB)

    # SEO and structure
    robots_txt_url = Column(Text)
    sitemap_urls = Column(ARRAY(Text))
    favicon_url = Column(Text)

    # Performance metrics
    avg_response_time = Column(Integer) # milliseconds
    uptime_percentage = Column(Float)
    ssl_enabled = Column(Boolean, default=False)
    mobile_friendly = Column(Boolean)

    # Content characteristics
    estimated_page_count = Column(Integer)
    content_freshness_score = Column(Float)
    update_frequency = Column(String(20)) # daily, weekly, monthly, etc.

    # Authority and trust
    domain_age_days = Column(Integer)
    backlink_count = Column(Integer)
    authority_score = Column(Float)
    spam_score = Column(Float)

    # Crawling metadata
    first_discovered = Column(DateTime, default=func.now())
    last_crawled = Column(DateTime)
    next_crawl_scheduled = Column(DateTime)
    crawl_frequency_hours = Column(Integer, default=168) # Weekly default
    crawl_priority = Column(Integer, default=5)

    # Status tracking
    status = Column(String(20), default='active') # active, blocked, error, suspended
    error_count = Column(Integer, default=0)
    last_error = Column(Text)

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    pages = relationship("Page", back_populates="site", cascade="all, delete-orphan")
    crawl_logs = relationship("CrawlLog", back_populates="site", cascade="all, delete-orphan")
