from sqlalchemy import Column, String, Integer, DateTime, Float, Boolean, Text, BigInteger, UUID as SQLAlchemyUUID
from sqlalchemy.sql import func
from models.base import Base # Assuming Base is in models/base.py
import uuid

class RequestHistory(Base):
    __tablename__ = 'request_history'

    id = Column(SQLAlchemyUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain = Column(String(255), nullable=False)
    url = Column(Text, nullable=False)
    worker_id = Column(String(100))

    # Request details
    method = Column(String(10), default='GET')
    status_code = Column(Integer)
    response_time_ms = Column(Integer)
    content_length = Column(BigInteger)

    # Success/failure tracking
    success = Column(Boolean)
    error_type = Column(String(50))
    error_message = Column(Text)

    # Timing
    requested_at = Column(DateTime, default=func.now())
    completed_at = Column(DateTime)

    def __repr__(self):
        return (f"<RequestHistory(id={self.id}, url='{self.url}', status_code='{self.status_code}', "
                f"success='{self.success}', requested_at='{self.requested_at}')>")
