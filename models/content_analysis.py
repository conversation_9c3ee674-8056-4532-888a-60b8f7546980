# SQLAlchemy models for NLP content analysis results
# Milestone 2.1: NLP Content Analysis Pipeline

from sqlalchemy import Column, String, Integer, DateTime, Text, Float, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from models.base import Base


class NLPAnalysis(Base):
    """SQLAlchemy model for NLP analysis results"""
    __tablename__ = 'nlp_analysis'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    page_id = Column(UUID(as_uuid=True), ForeignKey('pages.id', ondelete='CASCADE'), nullable=False)

    # Language detection
    detected_language = Column(String(10))
    language_confidence = Column(Float)

    # Topic classification
    primary_topic = Column(String(100))
    topic_confidence = Column(Float)
    topic_hierarchy = Column(JSONB)  # Hierarchical topic classification

    # Named entity recognition
    entities = Column(JSONB)  # {type: [entities]} format
    entity_count = Column(Integer)

    # Keyword extraction
    keywords = Column(JSONB)  # [{keyword, score, frequency}]
    key_phrases = Column(JSONB)

    # Sentiment analysis
    sentiment_polarity = Column(Float)  # -1 to 1
    sentiment_subjectivity = Column(Float)  # 0 to 1
    emotion_scores = Column(JSONB)  # joy, anger, fear, etc.

    # Content quality metrics
    readability_score = Column(Float)
    grammar_score = Column(Float)
    coherence_score = Column(Float)

    # Content structure analysis
    paragraph_count = Column(Integer)
    sentence_count = Column(Integer)
    avg_sentence_length = Column(Float)
    complexity_score = Column(Float)

    # Processing metadata
    model_version = Column(String(50))
    processing_time_ms = Column(Integer)
    processed_at = Column(DateTime, default=func.now())

    created_at = Column(DateTime, default=func.now())

    # Relationships
    page = relationship("Page", back_populates="nlp_analysis")

    def __repr__(self):
        return f"<NLPAnalysis(page_id={self.page_id}, primary_topic='{self.primary_topic}')>"


class TopicTaxonomy(Base):
    """SQLAlchemy model for hierarchical topic classification"""
    __tablename__ = 'topic_taxonomy'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    topic_name = Column(String(100), unique=True, nullable=False)
    parent_topic_id = Column(UUID(as_uuid=True), ForeignKey('topic_taxonomy.id'))
    level = Column(Integer, nullable=False)  # 0=root, 1=category, 2=subcategory, etc.
    description = Column(Text)
    keywords = Column(JSONB)  # Array of keywords

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Self-referential relationship for hierarchy
    parent = relationship("TopicTaxonomy", remote_side=[id], back_populates="children")
    children = relationship("TopicTaxonomy", back_populates="parent")

    def __repr__(self):
        return f"<TopicTaxonomy(topic_name='{self.topic_name}', level={self.level})>"
