import uuid
from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, JSON
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.sql import func
from models.base import Base

class RobotsCache(Base):
    __tablename__ = 'robots_cache'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain = Column(String(255), unique=True, nullable=False)
    robots_txt = Column(Text)
    parsed_rules = Column(JSON)  # Using generic JSON, as JSONB is specific to PostgreSQL dialect
    user_agent_rules = Column(JSON) # Using generic JSON
    crawl_delay = Column(Integer)
    sitemap_urls = Column(ARRAY(Text))

    # Metadata
    fetched_at = Column(DateTime, default=func.now())
    expires_at = Column(DateTime)
    fetch_attempts = Column(Integer, default=0)
    last_error = Column(Text)
    is_accessible = Column(Boolean, default=True)

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<RobotsCache(domain='{self.domain}', expires_at='{self.expires_at}')>"
