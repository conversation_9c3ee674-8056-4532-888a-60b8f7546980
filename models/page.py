# models/page.py
from sqlalchemy import Column, String, Text, Integer, DateTime, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import hashlib

from .base import Base

class Page(Base):
    """Model for crawled web pages"""
    __tablename__ = 'pages'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    site_id = Column(UUID(as_uuid=True), ForeignKey('sites.id', ondelete='CASCADE'), nullable=False, index=True)
    url = Column(Text, nullable=False, index=True)
    title = Column(Text)
    content_hash = Column(String(64), index=True)
    html_content = Column(Text)
    text_content = Column(Text)
    word_count = Column(Integer)
    language = Column(String(10))
    http_status = Column(Integer, index=True)
    content_type = Column(String(100))
    content_length = Column(Integer)
    crawled_at = Column(DateTime, default=func.now(), nullable=False, index=True)
    last_modified = Column(DateTime)
    etag = Column(String(255))
    is_duplicate = Column(Boolean, default=False)
    duplicate_of = Column(UUID(as_uuid=True), ForeignKey('pages.id', ondelete='SET NULL'))
    
    # Relationships
    site = relationship("Site", back_populates="pages")
    outbound_links = relationship("Link", foreign_keys="Link.from_page_id", back_populates="from_page")
    inbound_links = relationship("Link", foreign_keys="Link.to_page_id", back_populates="to_page")
    duplicate_pages = relationship("Page", remote_side=[id])
    
    def __repr__(self):
        return f"<Page(url='{self.url}', title='{self.title}', status={self.http_status})>"
    
    def calculate_content_hash(self, content=None):
        """Calculate SHA-256 hash of page content"""
        if content is None:
            content = self.text_content or self.html_content or ''
        
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def set_content(self, html_content, text_content=None):
        """Set page content and calculate hash"""
        self.html_content = html_content
        if text_content:
            self.text_content = text_content
            self.word_count = len(text_content.split())
        
        self.content_hash = self.calculate_content_hash(text_content or html_content)
    
    def is_successful(self):
        """Check if page was successfully crawled"""
        return self.http_status and 200 <= self.http_status < 300
    
    def is_redirect(self):
        """Check if page is a redirect"""
        return self.http_status and 300 <= self.http_status < 400
    
    def is_error(self):
        """Check if page returned an error"""
        return self.http_status and self.http_status >= 400
