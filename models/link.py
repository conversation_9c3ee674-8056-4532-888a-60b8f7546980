# models/link.py
from sqlalchemy import Column, String, Text, Integer, DateTime, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from .base import Base

class Link(Base):
    """Model for links between pages"""
    __tablename__ = 'links'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    from_page_id = Column(UUID(as_uuid=True), ForeignKey('pages.id', ondelete='CASCADE'), nullable=False, index=True)
    to_url = Column(Text, nullable=False, index=True)
    to_page_id = Column(UUID(as_uuid=True), ForeignKey('pages.id', ondelete='SET NULL'), index=True)
    anchor_text = Column(Text)
    link_type = Column(String(20), default='internal')  # internal, external, mailto, tel, etc.
    position = Column(Integer)  # Position of link on the page
    is_nofollow = Column(Boolean, default=False)
    discovered_at = Column(DateTime, default=func.now(), nullable=False)
    
    # Relationships
    from_page = relationship("Page", foreign_keys=[from_page_id], back_populates="outbound_links")
    to_page = relationship("Page", foreign_keys=[to_page_id], back_populates="inbound_links")
    
    def __repr__(self):
        return f"<Link(from='{self.from_page_id}', to='{self.to_url}', type='{self.link_type}')>"
    
    def is_external(self):
        """Check if this is an external link"""
        return self.link_type == 'external'
    
    def is_internal(self):
        """Check if this is an internal link"""
        return self.link_type == 'internal'
    
    def should_follow(self):
        """Check if this link should be followed by crawlers"""
        return not self.is_nofollow and self.link_type in ['internal', 'external']
