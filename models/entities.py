import uuid
from sqlalchemy import Column, String, Integer, DateTime, Text, Float, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from models.base import Base

class Entity(Base):
    __tablename__ = 'entities'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(500), nullable=False)
    entity_type = Column(String(50), nullable=False)
    canonical_name = Column(String(500))
    description = Column(Text)

    confidence_score = Column(Float)
    frequency_count = Column(Integer, default=1)
    first_mentioned = Column(DateTime, default=func.now())
    last_mentioned = Column(DateTime, default=func.now(), onupdate=func.now())

    wikipedia_id = Column(String(100))
    wikidata_id = Column(String(100))
    external_ids = Column(JSONB)

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    page_mentions = relationship("PageEntity", back_populates="entity")
    relationships1 = relationship("EntityRelationship", foreign_keys="[EntityRelationship.entity1_id]", back_populates="entity1")
    relationships2 = relationship("EntityRelationship", foreign_keys="[EntityRelationship.entity2_id]", back_populates="entity2")


class PageEntity(Base):
    __tablename__ = 'page_entities'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    page_id = Column(UUID(as_uuid=True), ForeignKey('pages.id', ondelete='CASCADE'))
    entity_id = Column(UUID(as_uuid=True), ForeignKey('entities.id', ondelete='CASCADE'))

    mention_text = Column(String(500))
    context_before = Column(Text)
    context_after = Column(Text)
    position_in_text = Column(Integer)

    confidence_score = Column(Float)
    mention_type = Column(String(20))

    created_at = Column(DateTime, default=func.now())

    # Relationships
    page = relationship("Page", back_populates="entity_mentions") # Assuming Page model has 'entity_mentions'
    entity = relationship("Entity", back_populates="page_mentions")


class EntityRelationship(Base):
    __tablename__ = 'entity_relationships'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    entity1_id = Column(UUID(as_uuid=True), ForeignKey('entities.id', ondelete='CASCADE'))
    entity2_id = Column(UUID(as_uuid=True), ForeignKey('entities.id', ondelete='CASCADE'))
    relationship_type = Column(String(50))

    co_occurrence_count = Column(Integer, default=1)
    relationship_strength = Column(Float)

    first_seen = Column(DateTime, default=func.now())
    last_seen = Column(DateTime, default=func.now(), onupdate=func.now())
    example_contexts = Column(JSONB)

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    entity1 = relationship("Entity", foreign_keys=[entity1_id], back_populates="relationships1")
    entity2 = relationship("Entity", foreign_keys=[entity2_id], back_populates="relationships2")
