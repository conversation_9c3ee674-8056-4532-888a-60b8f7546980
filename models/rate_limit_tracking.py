from sqlalchemy import Column, String, Integer, DateTime, Float, UUID as SQLAlchemyUUID
from sqlalchemy.sql import func
from models.base import Base # Assuming Base is in models/base.py
import uuid
import datetime as dt

class RateLimitTracking(Base):
    __tablename__ = 'rate_limit_tracking'

    id = Column(SQLAlchemyUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain = Column(String(255), nullable=False)
    worker_id = Column(String(100))

    # Request tracking
    requests_last_minute = Column(Integer, default=0)
    requests_last_hour = Column(Integer, default=0)
    requests_last_day = Column(Integer, default=0)

    # Timing
    last_request_at = Column(DateTime)
    next_allowed_request = Column(DateTime, default=func.now())

    # Adaptive rate limiting
    current_delay_ms = Column(Integer, default=1000)
    success_rate = Column(Float, default=1.0)
    consecutive_failures = Column(Integer, default=0)

    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return (f"<RateLimitTracking(id={self.id}, domain='{self.domain}', worker_id='{self.worker_id}', "
                f"next_allowed='{self.next_allowed_request}', delay_ms='{self.current_delay_ms}')>")
