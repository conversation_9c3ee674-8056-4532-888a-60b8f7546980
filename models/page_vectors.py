# models/page_vectors.py
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, JSONB, INET
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base
import uuid


class PageVector(Base):
    """Vector embeddings for pages"""

    __tablename__ = "page_vectors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    page_id = Column(
        UUID(as_uuid=True), ForeignKey("pages.id", ondelete="CASCADE"), nullable=False
    )

    # Vector embeddings stored as JSONB arrays (fallback for pgvector)
    content_embedding = Column(JSONB)  # Full content embedding as array
    title_embedding = Column(JSONB)  # Title-specific embedding as array
    summary_embedding = Column(JSONB)  # Summary embedding as array

    # Embedding metadata
    model_name = Column(String(100), nullable=False)
    model_version = Column(String(50), nullable=False)
    embedding_created_at = Column(DateTime, default=func.now())

    # Content metadata for search optimization
    content_length = Column(Integer)
    language = Column(String(10))
    content_hash = Column(String(64))

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    page = relationship("Page", back_populates="vectors")


class SearchQuery(Base):
    """Search query history for analytics"""

    __tablename__ = "search_queries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    query_text = Column(Text, nullable=False)
    query_embedding = Column(JSONB)  # Query embedding as JSONB array (fallback)

    # Search parameters
    search_type = Column(String(20))  # semantic, hybrid, keyword
    limit_requested = Column(Integer)
    filters_applied = Column(JSONB)

    # Results metadata
    results_count = Column(Integer)
    response_time_ms = Column(Integer)

    # User context (if applicable)
    user_id = Column(String(100))
    session_id = Column(String(100))
    ip_address = Column(INET)
    user_agent = Column(Text)

    created_at = Column(DateTime, default=func.now())
