from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, Float, ARRAY, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from models.base import Base # Assuming Base is defined in models.base

# Page model for comprehensive web content analysis

class Page(Base):
    __tablename__ = 'pages'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    site_id = Column(UUID(as_uuid=True), ForeignKey('sites.id', ondelete='CASCADE'))
    url = Column(Text, unique=True, nullable=False)
    url_hash = Column(String(64), unique=True, nullable=False)

    # Page metadata
    title = Column(Text)
    meta_description = Column(Text)
    meta_keywords = Column(ARRAY(Text))
    canonical_url = Column(Text)

    # Content
    content_text = Column(Text)
    content_html = Column(Text)
    content_length = Column(Integer)
    word_count = Column(Integer)

    # Structure
    heading_h1 = Column(ARRAY(Text))
    heading_h2 = Column(ARRAY(Text))
    heading_h3 = Column(ARRAY(Text))
    internal_links_count = Column(Integer)
    external_links_count = Column(Integer)
    image_count = Column(Integer)

    # Technical details
    http_status = Column(Integer)
    content_type = Column(String(100))
    charset = Column(String(50))
    response_time_ms = Column(Integer)

    # SEO metrics
    page_rank_score = Column(Float)
    readability_score = Column(Float)
    keyword_density = Column(JSONB)

    # Social signals
    social_shares_count = Column(Integer)
    comments_count = Column(Integer)

    # Content classification
    content_type_classification = Column(String(50)) # article, product, homepage, etc.
    topic_categories = Column(ARRAY(Text))
    sentiment_score = Column(Float)

    # Crawling metadata
    first_discovered = Column(DateTime, default=func.now())
    last_crawled = Column(DateTime, default=func.now())
    last_modified = Column(DateTime)
    etag = Column(String(255))

    # Change tracking
    content_hash = Column(String(64))
    previous_content_hash = Column(String(64))
    change_frequency = Column(String(20))

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    site = relationship("Site", back_populates="pages")
    outbound_links = relationship("Link", foreign_keys="Link.from_page_id", back_populates="from_page")
    entity_mentions = relationship("PageEntity", back_populates="page", cascade="all, delete-orphan")
    inbound_links = relationship("Link", foreign_keys="Link.to_page_id", back_populates="to_page")
    nlp_analysis = relationship("NLPAnalysis", back_populates="page", uselist=False)  # Milestone 2.1
