name: Web Crawler CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_pass
          POSTGRES_USER: test_user
          POSTGRES_DB: test_web_crawler
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: 3.12

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f requirements-dev.txt ]; then
          pip install -r requirements-dev.txt
        fi
        pip install -r requirements.txt

    - name: Run code quality checks
      run: |
        black --check .
        flake8 .
        mypy .
        bandit -r . -x tests/

    - name: Run tests
      env: # Set environment variables for the test database connection
        TEST_DB_USER: test_user
        TEST_DB_PASSWORD: test_pass
        TEST_DB_NAME: test_web_crawler
        TEST_DB_HOST: localhost # Service name 'postgres' is mapped to localhost by GitHub Actions
        TEST_DB_PORT: 5432
      run: |
        pytest tests/ -v --cov=. --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        # token: ${{ secrets.CODECOV_TOKEN }} # Optional: if you have a private repo or need to specify token

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Run security scan
      # This action seems to be a placeholder or a specific one from SecureCodeWarrior.
      # If it's not publicly available or requires specific setup, this step might fail.
      # Using a generic SARIF upload or a different scanner might be alternatives if needed.
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'security-scan-results.sarif' # This file needs to be generated by a scanner
    # Placeholder for actual security scan command if the above action doesn't run one.
    # For example, if using bandit to generate SARIF:
    # - name: Generate SARIF report with Bandit
    #   run: |
    #     pip install bandit
    #     bandit -r . -x tests/ -f sarif -o security-scan-results.sarif
    # - name: Upload SARIF file
    #   uses: github/codeql-action/upload-sarif@v2
    #   with:
    #     sarif_file: security-scan-results.sarif
