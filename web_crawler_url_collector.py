import requests
from bs4 import BeautifulSoup
import datetime

# Create a list to store the URLs
urls = []

# Create a loop to crawl the internet
for i in range(1, 10):
    # Get the HTML from the website
    response = requests.get('http://www.example.com/page' + str(i))
    html = response.content

    # Parse the HTML
    soup = BeautifulSoup(html, 'html.parser')

    # Find all the links
    links = soup.find_all('a')

    # Append each link to the list
    for link in links:
        urls.append(link.get('href'))

# Create a filename based on the current date and time
filename = datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S') + '.txt'

# Open the file in write mode
with open(filename, 'w') as f:
    # Write each URL to the file
    for url in urls:
        f.write(url + '\n')

# Close the file
f.close()

