# Universal Web Directory - Comprehensive Development Roadmap

**Target**: Transform current prototype to enterprise-scale Universal Web Directory
**Phases**: 8 development phases


---

## 🎯 **EXECUTIVE SUMMARY**

This roadmap transforms the current 2-file prototype (`url_discovery.py`, `content_scraper.py`) into a comprehensive web intelligence infrastructure capable of:

- **Scale**: Processing 100M+ URLs with distributed architecture
- **Intelligence**: Advanced NLP, semantic search, and trend detection
- **Multi-Consumer**: API-first design serving diverse applications
- **Real-Time**: Live monitoring, change detection, and trend analysis

**Current State**: Basic prototype (≈2% complete)
**Target State**: Production-ready Universal Web Directory platform

---

## 📊 **DEVELOPMENT PHASES OVERVIEW**

+----------+---------------------------+----------------------------------------------+------------------------------------------+----------------------------+
| Phase    | Focus                     | Deliverables                                 | Milestones                               | Tools/Technologies         |
+----------+---------------------------+----------------------------------------------+------------------------------------------+----------------------------+
| Phase 0  | Environment & Migration   | PostgreSQL setup, seed data integration      | DB ready with dev data                   | PostgreSQL, pgAdmin        | ------------> complete
| Phase 1  | Foundation Infrastructure | Robust crawler, database architecture        | Crawler MVP deployed                     | Python, Scrapy, SQLAlchemy |
| Phase 2  | Intelligence Layer        | NLP pipeline, content analysis               | NLP output summary reports               | spaCy, NLTK, HuggingFace   |
| Phase 3  | Search & API              | Vector search, REST API, query interface     | Search results via API                   | FastAPI, Faiss, Swagger    |
| Phase 4  | Advanced Features         | Knowledge graphs, trend detection            | Graph visualizations, trend alerts       | NetworkX, D3.js            |
| Phase 5  | Scale & Performance       | Distributed architecture, optimization       | Load testing benchmarks met              | Docker, Kubernetes, Redis  |
| Phase 6  | Production Deployment     | Monitoring, CI/CD, security                  | Full production readiness                | Prometheus, GitHub Actions |
| Phase 7  | Advanced Intelligence     | Predictive analytics, quality scoring        | Accuracy metrics and dashboards          | XGBoost, Grafana, MLFlow   |
| Phase 8  | Enterprise Integration    | Multi-tenant architecture, API ecosystem     | Enterprise customer deployments          | Terraform, Auth0, Stripe   |
+----------+---------------------------+----------------------------------------------+------------------------------------------+----------------------------+


---

## 🚀 **PHASE 0: ENVIRONMENT SETUP & SEED DATA INTEGRATION**
**Priority**: Critical |

### **Milestone 0.1: PostgreSQL Database Setup** ------------> complete

**Objective**: Establish production-ready PostgreSQL environment with proper configuration

**Technical Implementation**:
```sql
-- Database creation and configuration
CREATE DATABASE universal_web_directory;
CREATE USER uwd_app WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE universal_web_directory TO uwd_app;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";  -- For fuzzy text search
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- For composite indexes
CREATE EXTENSION IF NOT EXISTS "pgvector";  -- For vector embeddings (install separately)

-- Performance tuning
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
SELECT pg_reload_conf();
```

**Configuration Management**:
```python
# config/database.py
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base

class DatabaseConfig:
    def __init__(self):
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = os.getenv('DB_PORT', '5432')
        self.database = os.getenv('DB_NAME', 'universal_web_directory')
        self.username = os.getenv('DB_USER', 'uwd_app')
        self.password = os.getenv('DB_PASSWORD')

    @property
    def connection_string(self):
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

# Database session management
engine = create_engine(DatabaseConfig().connection_string,
                      pool_size=20, max_overflow=30, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
```

### **Milestone 0.2: Seed Data Integration**  ------------> complete

**Objective**: Integrate directory_link_source/links_to_directory_websites.txt as initial crawl targets

**Implementation**:
```python
# scripts/seed_data_loader.py
import os
import sys
from urllib.parse import urlparse
from sqlalchemy.orm import Session
from models.url_frontier import URLFrontier
from config.database import SessionLocal

class SeedDataLoader:
    def __init__(self, seed_file_path: str):
        self.seed_file_path = seed_file_path
        self.session = SessionLocal()

    def load_directory_websites(self):
        """Load directory websites as high-priority seed URLs"""
        with open(self.seed_file_path, 'r') as f:
            urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]

        loaded_count = 0
        for url in urls:
            if self._is_valid_url(url):
                try:
                    seed_url = URLFrontier(
                        url=url,
                        priority=10,  # High priority for directory sites
                        source_type='seed_directory',
                        discovered_at=datetime.utcnow(),
                        status='pending'
                    )
                    self.session.add(seed_url)
                    loaded_count += 1
                except Exception as e:
                    print(f"Error loading {url}: {e}")

        self.session.commit()
        print(f"Loaded {loaded_count} directory websites as seed URLs")
        return loaded_count

    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format and accessibility"""
        try:
            parsed = urlparse(url)
            return all([parsed.scheme, parsed.netloc])
        except:
            return False

# Usage
if __name__ == "__main__":
    loader = SeedDataLoader("directory_link_source/links_to_directory_websites.txt")
    loader.load_directory_websites()
```

### **Milestone 0.3: Project Structure Reorganization** ------------> complete

**Objective**: Transform flat file structure into modular, scalable architecture

**New Project Structure**:
```
universal_web_directory/
├── README.md
├── requirements.txt
├── setup.py
├── .env.example
├── docker-compose.yml
├── config/
│   ├── __init__.py
│   ├── database.py
│   ├── crawler.py
│   ├── nlp.py
│   └── api.py
├── models/
│   ├── __init__.py
│   ├── base.py
│   ├── url_frontier.py
│   ├── sites.py
│   ├── pages.py
│   ├── content_analysis.py
│   └── relationships.py
├── services/
│   ├── __init__.py
│   ├── crawler/
│   │   ├── __init__.py
│   │   ├── url_discovery.py
│   │   ├── content_scraper.py
│   │   ├── robots_handler.py
│   │   └── rate_limiter.py
│   ├── nlp/
│   │   ├── __init__.py
│   │   ├── content_analyzer.py
│   │   ├── entity_extractor.py
│   │   ├── topic_classifier.py
│   │   └── summarizer.py
│   ├── search/
│   │   ├── __init__.py
│   │   ├── vector_search.py
│   │   ├── structured_search.py
│   │   └── hybrid_search.py
│   └── api/
│       ├── __init__.py
│       ├── main.py
│       ├── routes/
│       └── middleware/
├── scripts/
│   ├── seed_data_loader.py
│   ├── database_migration.py
│   ├── performance_monitor.py
│   └── data_quality_checker.py
├── tests/
│   ├── unit/
│   ├── integration/
│   └── performance/
├── monitoring/
│   ├── prometheus/
│   ├── grafana/
│   └── alerts/
├── deployment/
│   ├── kubernetes/
│   ├── terraform/
│   └── ansible/
└── data/
    ├── seed_urls/
    ├── models/
    └── exports/
```

### **Milestone 0.4: Legacy Code Migration** ------------> complete

**Objective**: Refactor existing url_discovery.py and content_scraper.py into new architecture

**Migration Strategy**:
```python
# services/crawler/legacy_migrator.py
class LegacyMigrator:
    """Migrate existing functionality to new architecture"""

    def migrate_url_discovery(self):
        """Convert url_discovery.py to new URLDiscoveryService"""
        # Extract core logic
        # Add error handling
        # Integrate with database
        # Add configuration management
        pass

    def migrate_content_scraper(self):
        """Convert content_scraper.py to new ContentScrapingService"""
        # Extract core logic
        # Add robust error handling
        # Integrate with database
        # Add content validation
        pass
```

**Success Criteria**:
- [ ] PostgreSQL database operational with proper configuration
- [ ] All 127 directory websites loaded as seed URLs
- [ ] New project structure implemented
- [ ] Legacy code successfully migrated
- [ ] Basic tests passing
- [ ] Documentation updated

---

## 🏗️ **PHASE 1: FOUNDATION INFRASTRUCTURE**
**Priority**: Critical |

### **Milestone 1.1: Advanced URL Frontier System** ------------> complete

**Objective**: Replace file-based URL storage with enterprise-grade queue system

**Database Schema**:
```sql
-- Core URL frontier table
CREATE TABLE url_frontier (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT UNIQUE NOT NULL,
    url_hash VARCHAR(64) UNIQUE NOT NULL, -- SHA-256 for deduplication
    domain VARCHAR(255) NOT NULL,
    subdomain VARCHAR(255),
    path TEXT,
    query_params JSONB,

    -- Crawling metadata
    status VARCHAR(20) DEFAULT 'pending', -- pending, crawling, crawled, error, blocked
    priority INTEGER DEFAULT 5, -- 1-10 scale, 10 = highest
    source_type VARCHAR(50), -- seed, discovered, sitemap, robots
    discovered_at TIMESTAMP DEFAULT NOW(),
    scheduled_for TIMESTAMP DEFAULT NOW(),
    last_crawled TIMESTAMP,
    next_crawl TIMESTAMP,
    crawl_attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,

    -- Error handling
    error_message TEXT,
    error_type VARCHAR(50),
    http_status INTEGER,

    -- Metadata
    content_type VARCHAR(100),
    content_length BIGINT,
    last_modified TIMESTAMP,
    etag VARCHAR(255),

    -- Indexing and performance
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_url_frontier_status ON url_frontier(status);
CREATE INDEX idx_url_frontier_priority ON url_frontier(priority DESC);
CREATE INDEX idx_url_frontier_domain ON url_frontier(domain);
CREATE INDEX idx_url_frontier_scheduled ON url_frontier(scheduled_for) WHERE status = 'pending';
CREATE INDEX idx_url_frontier_next_crawl ON url_frontier(next_crawl) WHERE status = 'crawled';
CREATE UNIQUE INDEX idx_url_frontier_hash ON url_frontier(url_hash);

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_url_frontier_updated_at BEFORE UPDATE
    ON url_frontier FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

**SQLAlchemy Model Implementation**:
```python
# models/url_frontier.py
from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, BIGINT
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
import hashlib
import uuid
from models.base import Base

class URLFrontier(Base):
    __tablename__ = 'url_frontier'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    url = Column(String, unique=True, nullable=False)
    url_hash = Column(String(64), unique=True, nullable=False)
    domain = Column(String(255), nullable=False)
    subdomain = Column(String(255))
    path = Column(Text)
    query_params = Column(JSONB)

    # Crawling metadata
    status = Column(String(20), default='pending')
    priority = Column(Integer, default=5)
    source_type = Column(String(50))
    discovered_at = Column(DateTime, default=func.now())
    scheduled_for = Column(DateTime, default=func.now())
    last_crawled = Column(DateTime)
    next_crawl = Column(DateTime)
    crawl_attempts = Column(Integer, default=0)
    max_attempts = Column(Integer, default=3)

    # Error handling
    error_message = Column(Text)
    error_type = Column(String(50))
    http_status = Column(Integer)

    # Metadata
    content_type = Column(String(100))
    content_length = Column(BIGINT)
    last_modified = Column(DateTime)
    etag = Column(String(255))

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __init__(self, url: str, **kwargs):
        self.url = url
        self.url_hash = hashlib.sha256(url.encode()).hexdigest()
        from urllib.parse import urlparse
        parsed = urlparse(url)
        self.domain = parsed.netloc
        self.subdomain = parsed.hostname
        self.path = parsed.path
        if parsed.query:
            import json
            from urllib.parse import parse_qs
            self.query_params = json.dumps(parse_qs(parsed.query))
        super().__init__(**kwargs)

# URL Frontier Service
class URLFrontierService:
    def __init__(self, session):
        self.session = session

    def add_url(self, url: str, priority: int = 5, source_type: str = 'discovered') -> bool:
        """Add URL to frontier with deduplication"""
        try:
            url_entry = URLFrontier(url=url, priority=priority, source_type=source_type)
            self.session.add(url_entry)
            self.session.commit()
            return True
        except Exception as e:
            self.session.rollback()
            return False

    def get_next_urls(self, limit: int = 10, worker_id: str = None) -> list:
        """Get next URLs for crawling with distributed locking"""
        query = """
        UPDATE url_frontier
        SET status = 'crawling',
            updated_at = NOW(),
            worker_id = %s
        WHERE id IN (
            SELECT id FROM url_frontier
            WHERE status = 'pending'
            AND scheduled_for <= NOW()
            ORDER BY priority DESC, scheduled_for ASC
            LIMIT %s
            FOR UPDATE SKIP LOCKED
        )
        RETURNING id, url, priority, crawl_attempts;
        """
        result = self.session.execute(query, (worker_id, limit))
        return result.fetchall()

    def mark_crawled(self, url_id: str, success: bool = True, error_msg: str = None):
        """Mark URL as crawled or failed"""
        url_entry = self.session.query(URLFrontier).filter_by(id=url_id).first()
        if url_entry:
            if success:
                url_entry.status = 'crawled'
                url_entry.last_crawled = func.now()
                url_entry.next_crawl = func.now() + timedelta(days=7)  # Recrawl weekly
            else:
                url_entry.crawl_attempts += 1
                if url_entry.crawl_attempts >= url_entry.max_attempts:
                    url_entry.status = 'error'
                else:
                    url_entry.status = 'pending'
                    url_entry.scheduled_for = func.now() + timedelta(hours=1)  # Retry in 1 hour
                url_entry.error_message = error_msg
            self.session.commit()
```

### **Milestone 1.2: Robots.txt Compliance System** ------------> complete

**Objective**: Implement comprehensive robots.txt handling with caching and compliance checking

**Database Schema**:
```sql
-- Robots.txt cache and rules
CREATE TABLE robots_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) UNIQUE NOT NULL,
    robots_txt TEXT,
    parsed_rules JSONB, -- Structured rules for faster processing
    user_agent_rules JSONB, -- Rules per user agent
    crawl_delay INTEGER, -- Delay in seconds
    sitemap_urls TEXT[], -- Array of sitemap URLs

    -- Metadata
    fetched_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    fetch_attempts INTEGER DEFAULT 0,
    last_error TEXT,
    is_accessible BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_robots_domain ON robots_cache(domain);
CREATE INDEX idx_robots_expires ON robots_cache(expires_at);

-- Domain crawling policies
CREATE TABLE domain_policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) UNIQUE NOT NULL,

    -- Crawling policies
    is_allowed BOOLEAN DEFAULT TRUE,
    crawl_delay INTEGER DEFAULT 1, -- Seconds between requests
    max_concurrent_requests INTEGER DEFAULT 1,
    respect_robots_txt BOOLEAN DEFAULT TRUE,

    -- Rate limiting
    requests_per_minute INTEGER DEFAULT 60,
    requests_per_hour INTEGER DEFAULT 3600,
    requests_per_day INTEGER DEFAULT 86400,

    -- Quality metrics
    success_rate FLOAT DEFAULT 1.0,
    avg_response_time INTEGER, -- Milliseconds
    last_successful_crawl TIMESTAMP,
    consecutive_failures INTEGER DEFAULT 0,

    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_domain_policies_domain ON domain_policies(domain);
CREATE INDEX idx_domain_policies_allowed ON domain_policies(is_allowed);
```

**Implementation**:
```python
# services/crawler/robots_handler.py
import urllib.robotparser
import requests
from urllib.parse import urljoin, urlparse
from typing import Optional, List, Dict
import json
from datetime import datetime, timedelta

class RobotsHandler:
    def __init__(self, session, user_agent: str = "UniversalWebDirectory/1.0"):
        self.session = session
        self.user_agent = user_agent
        self.cache_duration = timedelta(days=1)  # Cache robots.txt for 1 day

    def can_crawl(self, url: str) -> bool:
        """Check if URL can be crawled according to robots.txt"""
        domain = urlparse(url).netloc
        robots_data = self._get_robots_data(domain)

        if not robots_data or not robots_data.is_accessible:
            return True  # If robots.txt not accessible, assume allowed

        # Parse robots.txt rules
        rp = urllib.robotparser.RobotFileParser()
        rp.set_url(f"https://{domain}/robots.txt")
        if robots_data.robots_txt:
            rp.read()
            return rp.can_fetch(self.user_agent, url)

        return True

    def get_crawl_delay(self, domain: str) -> int:
        """Get crawl delay for domain from robots.txt"""
        robots_data = self._get_robots_data(domain)
        if robots_data and robots_data.crawl_delay:
            return robots_data.crawl_delay

        # Check domain policies
        policy = self._get_domain_policy(domain)
        return policy.crawl_delay if policy else 1

    def get_sitemaps(self, domain: str) -> List[str]:
        """Get sitemap URLs from robots.txt"""
        robots_data = self._get_robots_data(domain)
        return robots_data.sitemap_urls if robots_data else []

    def _get_robots_data(self, domain: str):
        """Get cached robots.txt data or fetch if expired"""
        from models.robots_cache import RobotsCache

        # Check cache
        cached = self.session.query(RobotsCache).filter_by(domain=domain).first()

        if cached and cached.expires_at > datetime.utcnow():
            return cached

        # Fetch new robots.txt
        return self._fetch_robots_txt(domain)

    def _fetch_robots_txt(self, domain: str):
        """Fetch and parse robots.txt"""
        from models.robots_cache import RobotsCache

        robots_url = f"https://{domain}/robots.txt"

        try:
            response = requests.get(robots_url, timeout=10,
                                  headers={'User-Agent': self.user_agent})

            if response.status_code == 200:
                robots_txt = response.text
                parsed_rules = self._parse_robots_txt(robots_txt)

                # Update or create cache entry
                cached = self.session.query(RobotsCache).filter_by(domain=domain).first()
                if not cached:
                    cached = RobotsCache(domain=domain)

                cached.robots_txt = robots_txt
                cached.parsed_rules = json.dumps(parsed_rules)
                cached.crawl_delay = parsed_rules.get('crawl_delay', 1)
                cached.sitemap_urls = parsed_rules.get('sitemaps', [])
                cached.fetched_at = datetime.utcnow()
                cached.expires_at = datetime.utcnow() + self.cache_duration
                cached.is_accessible = True

                self.session.add(cached)
                self.session.commit()

                return cached

        except Exception as e:
            # Mark as inaccessible
            cached = self.session.query(RobotsCache).filter_by(domain=domain).first()
            if not cached:
                cached = RobotsCache(domain=domain)

            cached.is_accessible = False
            cached.last_error = str(e)
            cached.fetch_attempts += 1
            cached.expires_at = datetime.utcnow() + timedelta(hours=6)  # Retry in 6 hours

            self.session.add(cached)
            self.session.commit()

            return cached

    def _parse_robots_txt(self, robots_txt: str) -> Dict:
        """Parse robots.txt content into structured data"""
        rules = {
            'user_agents': {},
            'sitemaps': [],
            'crawl_delay': None
        }

        current_user_agent = None

        for line in robots_txt.split('\n'):
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower()
                value = value.strip()

                if key == 'user-agent':
                    current_user_agent = value
                    if current_user_agent not in rules['user_agents']:
                        rules['user_agents'][current_user_agent] = {
                            'allow': [],
                            'disallow': [],
                            'crawl_delay': None
                        }

                elif key == 'disallow' and current_user_agent:
                    rules['user_agents'][current_user_agent]['disallow'].append(value)

                elif key == 'allow' and current_user_agent:
                    rules['user_agents'][current_user_agent]['allow'].append(value)

                elif key == 'crawl-delay' and current_user_agent:
                    try:
                        delay = int(value)
                        rules['user_agents'][current_user_agent]['crawl_delay'] = delay
                        if not rules['crawl_delay']:
                            rules['crawl_delay'] = delay
                    except ValueError:
                        pass

                elif key == 'sitemap':
                    rules['sitemaps'].append(value)

        return rules
```

### **Milestone 1.3: Advanced Rate Limiting & Politeness System** ------------> complete

**Objective**: Implement sophisticated rate limiting to prevent server overload and IP blocking

**Database Schema**:
```sql
-- Rate limiting tracking
CREATE TABLE rate_limit_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) NOT NULL,
    worker_id VARCHAR(100),

    -- Request tracking
    requests_last_minute INTEGER DEFAULT 0,
    requests_last_hour INTEGER DEFAULT 0,
    requests_last_day INTEGER DEFAULT 0,

    -- Timing
    last_request_at TIMESTAMP,
    next_allowed_request TIMESTAMP DEFAULT NOW(),

    -- Adaptive rate limiting
    current_delay_ms INTEGER DEFAULT 1000,
    success_rate FLOAT DEFAULT 1.0,
    consecutive_failures INTEGER DEFAULT 0,

    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_rate_limit_domain_worker ON rate_limit_tracking(domain, worker_id);
CREATE INDEX idx_rate_limit_next_allowed ON rate_limit_tracking(next_allowed_request);

-- Request history for analytics
CREATE TABLE request_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    worker_id VARCHAR(100),

    -- Request details
    method VARCHAR(10) DEFAULT 'GET',
    status_code INTEGER,
    response_time_ms INTEGER,
    content_length BIGINT,

    -- Success/failure tracking
    success BOOLEAN,
    error_type VARCHAR(50),
    error_message TEXT,

    -- Timing
    requested_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

CREATE INDEX idx_request_history_domain ON request_history(domain);
CREATE INDEX idx_request_history_requested_at ON request_history(requested_at);
CREATE INDEX idx_request_history_success ON request_history(success);
```

**Implementation**:
```python
# services/crawler/rate_limiter.py
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Optional
from collections import defaultdict, deque
import threading

class AdaptiveRateLimiter:
    """Advanced rate limiter with adaptive delays and distributed coordination"""

    def __init__(self, session, worker_id: str):
        self.session = session
        self.worker_id = worker_id
        self.local_cache = {}  # Local rate limit cache
        self.request_history = defaultdict(lambda: deque(maxlen=1000))
        self.lock = threading.Lock()

        # Default policies
        self.default_delay = 1.0  # seconds
        self.max_delay = 30.0  # seconds
        self.min_delay = 0.1  # seconds
        self.adaptive_factor = 1.2  # Increase delay by 20% on failure
        self.recovery_factor = 0.9  # Decrease delay by 10% on success

    async def wait_if_needed(self, domain: str) -> float:
        """Wait if needed before making request, returns actual delay"""
        with self.lock:
            # Get current rate limit state
            rate_state = self._get_rate_limit_state(domain)

            # Calculate required delay
            required_delay = self._calculate_delay(domain, rate_state)

            if required_delay > 0:
                await asyncio.sleep(required_delay)

            # Update rate limit state
            self._update_rate_limit_state(domain, rate_state)

            return required_delay

    def record_request_result(self, domain: str, url: str, success: bool,
                            response_time_ms: int, status_code: int = None):
        """Record request result for adaptive rate limiting"""
        # Implementation details for tracking success/failure rates
        # and adjusting delays accordingly
        pass
```

### **Milestone 1.4: Comprehensive Database Architecture** ------------> complete

**Objective**: Design and implement complete database schema for Universal Web Directory

**Core Database Schema**:
```sql
-- Sites table - Domain-level information
CREATE TABLE sites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) UNIQUE NOT NULL,
    subdomain VARCHAR(255),

    -- Site metadata
    title TEXT,
    description TEXT,
    language VARCHAR(10),
    country VARCHAR(3),

    -- Technical information
    ip_address INET,
    server_software VARCHAR(100),
    cms_platform VARCHAR(100),
    technology_stack JSONB,

    -- SEO and structure
    robots_txt_url TEXT,
    sitemap_urls TEXT[],
    favicon_url TEXT,

    -- Performance metrics
    avg_response_time INTEGER, -- milliseconds
    uptime_percentage FLOAT,
    ssl_enabled BOOLEAN DEFAULT FALSE,
    mobile_friendly BOOLEAN,

    -- Content characteristics
    estimated_page_count INTEGER,
    content_freshness_score FLOAT,
    update_frequency VARCHAR(20), -- daily, weekly, monthly, etc.

    -- Authority and trust
    domain_age_days INTEGER,
    backlink_count INTEGER,
    authority_score FLOAT,
    spam_score FLOAT,

    -- Crawling metadata
    first_discovered TIMESTAMP DEFAULT NOW(),
    last_crawled TIMESTAMP,
    next_crawl_scheduled TIMESTAMP,
    crawl_frequency_hours INTEGER DEFAULT 168, -- Weekly default
    crawl_priority INTEGER DEFAULT 5,

    -- Status tracking
    status VARCHAR(20) DEFAULT 'active', -- active, blocked, error, suspended
    error_count INTEGER DEFAULT 0,
    last_error TEXT,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for sites table
CREATE INDEX idx_sites_domain ON sites(domain);
CREATE INDEX idx_sites_last_crawled ON sites(last_crawled);
CREATE INDEX idx_sites_next_crawl ON sites(next_crawl_scheduled);
CREATE INDEX idx_sites_status ON sites(status);
CREATE INDEX idx_sites_authority_score ON sites(authority_score);

-- Pages table - Individual page information
CREATE TABLE pages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    url TEXT UNIQUE NOT NULL,
    url_hash VARCHAR(64) UNIQUE NOT NULL,

    -- Page metadata
    title TEXT,
    meta_description TEXT,
    meta_keywords TEXT[],
    canonical_url TEXT,

    -- Content
    content_text TEXT,
    content_html TEXT,
    content_length INTEGER,
    word_count INTEGER,

    -- Structure
    heading_h1 TEXT[],
    heading_h2 TEXT[],
    heading_h3 TEXT[],
    internal_links_count INTEGER,
    external_links_count INTEGER,
    image_count INTEGER,

    -- Technical details
    http_status INTEGER,
    content_type VARCHAR(100),
    charset VARCHAR(50),
    response_time_ms INTEGER,

    -- SEO metrics
    page_rank_score FLOAT,
    readability_score FLOAT,
    keyword_density JSONB,

    -- Social signals
    social_shares_count INTEGER,
    comments_count INTEGER,

    -- Content classification
    content_type_classification VARCHAR(50), -- article, product, homepage, etc.
    topic_categories TEXT[],
    sentiment_score FLOAT,

    -- Crawling metadata
    first_discovered TIMESTAMP DEFAULT NOW(),
    last_crawled TIMESTAMP DEFAULT NOW(),
    last_modified TIMESTAMP,
    etag VARCHAR(255),

    -- Change tracking
    content_hash VARCHAR(64),
    previous_content_hash VARCHAR(64),
    change_frequency VARCHAR(20),

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for pages table
CREATE INDEX idx_pages_site_id ON pages(site_id);
CREATE INDEX idx_pages_url_hash ON pages(url_hash);
CREATE INDEX idx_pages_last_crawled ON pages(last_crawled);
CREATE INDEX idx_pages_content_type ON pages(content_type_classification);
CREATE INDEX idx_pages_topic_categories ON pages USING GIN(topic_categories);
CREATE INDEX idx_pages_http_status ON pages(http_status);
```

**SQLAlchemy Models Implementation**:
```python
# models/sites.py
from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, Float, ARRAY
from sqlalchemy.dialects.postgresql import UUID, JSONB, INET
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from models.base import Base

class Site(Base):
    __tablename__ = 'sites'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain = Column(String(255), unique=True, nullable=False)
    subdomain = Column(String(255))

    # Site metadata
    title = Column(Text)
    description = Column(Text)
    language = Column(String(10))
    country = Column(String(3))

    # Technical information
    ip_address = Column(INET)
    server_software = Column(String(100))
    cms_platform = Column(String(100))
    technology_stack = Column(JSONB)

    # Performance metrics
    avg_response_time = Column(Integer)
    uptime_percentage = Column(Float)
    ssl_enabled = Column(Boolean, default=False)
    mobile_friendly = Column(Boolean)

    # Authority and trust
    domain_age_days = Column(Integer)
    backlink_count = Column(Integer)
    authority_score = Column(Float)
    spam_score = Column(Float)

    # Crawling metadata
    first_discovered = Column(DateTime, default=func.now())
    last_crawled = Column(DateTime)
    next_crawl_scheduled = Column(DateTime)
    crawl_frequency_hours = Column(Integer, default=168)
    crawl_priority = Column(Integer, default=5)

    # Status tracking
    status = Column(String(20), default='active')
    error_count = Column(Integer, default=0)
    last_error = Column(Text)

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    pages = relationship("Page", back_populates="site", cascade="all, delete-orphan")

# models/pages.py
class Page(Base):
    __tablename__ = 'pages'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    site_id = Column(UUID(as_uuid=True), ForeignKey('sites.id', ondelete='CASCADE'))
    url = Column(Text, unique=True, nullable=False)
    url_hash = Column(String(64), unique=True, nullable=False)

    # Page metadata
    title = Column(Text)
    meta_description = Column(Text)
    meta_keywords = Column(ARRAY(Text))
    canonical_url = Column(Text)

    # Content
    content_text = Column(Text)
    content_html = Column(Text)
    content_length = Column(Integer)
    word_count = Column(Integer)

    # Technical details
    http_status = Column(Integer)
    content_type = Column(String(100))
    charset = Column(String(50))
    response_time_ms = Column(Integer)

    # Content classification
    content_type_classification = Column(String(50))
    topic_categories = Column(ARRAY(Text))
    sentiment_score = Column(Float)

    # Crawling metadata
    first_discovered = Column(DateTime, default=func.now())
    last_crawled = Column(DateTime, default=func.now())
    last_modified = Column(DateTime)
    etag = Column(String(255))

    # Change tracking
    content_hash = Column(String(64))
    previous_content_hash = Column(String(64))
    change_frequency = Column(String(20))

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    site = relationship("Site", back_populates="pages")
    content_analysis = relationship("ContentAnalysis", back_populates="page", uselist=False)
```

### **Milestone 1.5: Enhanced Content Scraping Service**

**Objective**: Transform basic content_scraper.py into enterprise-grade content extraction system

**Implementation**:
```python
# services/crawler/content_scraper.py
import asyncio
import aiohttp
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

class EnhancedContentScraper:
    """Enterprise-grade content scraping with error handling and metadata extraction"""

    def __init__(self, session, rate_limiter, robots_handler):
        self.session = session
        self.rate_limiter = rate_limiter
        self.robots_handler = robots_handler
        self.user_agent = "UniversalWebDirectory/1.0 (+https://example.com/bot)"

    async def scrape_page(self, url: str) -> Dict:
        """Scrape a single page with comprehensive metadata extraction"""
        domain = urlparse(url).netloc

        # Check robots.txt compliance
        if not self.robots_handler.can_crawl(url):
            return {'error': 'Blocked by robots.txt', 'url': url}

        # Apply rate limiting
        await self.rate_limiter.wait_if_needed(domain)

        try:
            async with aiohttp.ClientSession() as client:
                start_time = datetime.now()

                async with client.get(
                    url,
                    headers={'User-Agent': self.user_agent},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    end_time = datetime.now()
                    response_time_ms = int((end_time - start_time).total_seconds() * 1000)

                    content = await response.text()

                    # Record request result
                    self.rate_limiter.record_request_result(
                        domain=domain,
                        url=url,
                        success=response.status == 200,
                        response_time_ms=response_time_ms,
                        status_code=response.status
                    )

                    if response.status == 200:
                        return await self._extract_page_data(url, content, response)
                    else:
                        return {
                            'error': f'HTTP {response.status}',
                            'url': url,
                            'status_code': response.status
                        }

        except Exception as e:
            self.rate_limiter.record_request_result(
                domain=domain,
                url=url,
                success=False,
                response_time_ms=0,
                error_type=type(e).__name__,
                error_message=str(e)
            )
            return {'error': str(e), 'url': url}

    async def _extract_page_data(self, url: str, content: str, response) -> Dict:
        """Extract comprehensive page data and metadata"""
        soup = BeautifulSoup(content, 'html.parser')

        # Basic metadata
        title = soup.find('title')
        title_text = title.get_text().strip() if title else ''

        meta_description = soup.find('meta', attrs={'name': 'description'})
        description = meta_description.get('content', '') if meta_description else ''

        # Content extraction
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()

        text_content = soup.get_text()
        clean_text = ' '.join(text_content.split())

        # Extract headings
        h1_tags = [h.get_text().strip() for h in soup.find_all('h1')]
        h2_tags = [h.get_text().strip() for h in soup.find_all('h2')]
        h3_tags = [h.get_text().strip() for h in soup.find_all('h3')]

        # Extract links
        links = []
        for link in soup.find_all('a', href=True):
            href = link['href']
            absolute_url = urljoin(url, href)
            anchor_text = link.get_text().strip()
            links.append({
                'url': absolute_url,
                'anchor_text': anchor_text,
                'is_internal': urlparse(absolute_url).netloc == urlparse(url).netloc
            })

        # Count elements
        internal_links = sum(1 for link in links if link['is_internal'])
        external_links = len(links) - internal_links
        image_count = len(soup.find_all('img'))

        # Generate content hash
        content_hash = hashlib.sha256(clean_text.encode()).hexdigest()

        return {
            'url': url,
            'title': title_text,
            'meta_description': description,
            'content_text': clean_text,
            'content_html': str(soup),
            'content_length': len(content),
            'word_count': len(clean_text.split()),
            'heading_h1': h1_tags,
            'heading_h2': h2_tags,
            'heading_h3': h3_tags,
            'internal_links_count': internal_links,
            'external_links_count': external_links,
            'image_count': image_count,
            'links': links,
            'content_hash': content_hash,
            'http_status': response.status,
            'content_type': response.headers.get('content-type', ''),
            'last_modified': response.headers.get('last-modified'),
            'etag': response.headers.get('etag'),
            'scraped_at': datetime.utcnow().isoformat()
        }
```

**Success Criteria for Phase 1**:
- [ ] PostgreSQL database with complete schema operational
- [ ] URL frontier system handling 10,000+ URLs efficiently
- [ ] Robots.txt compliance system with 99%+ accuracy
- [ ] Rate limiting preventing IP blocks across 100+ domains
- [ ] Enhanced content scraper extracting 50+ metadata fields per page
- [ ] Database storing crawled data with proper relationships
- [ ] System processing 1,000 pages/hour per worker
- [ ] Comprehensive error handling and recovery mechanisms
- [ ] Performance monitoring and alerting systems
- [ ] Complete test suite with 90%+ code coverage

---

## 🧠 **PHASE 2: INTELLIGENCE LAYER**
**Priority**: High |

### **Milestone 2.1: NLP Content Analysis Pipeline** ------------> complete

**Objective**: Implement advanced NLP processing for content understanding and classification

**Database Schema Extensions**:
```sql
-- NLP processing results
CREATE TABLE nlp_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    page_id UUID REFERENCES pages(id) ON DELETE CASCADE,

    -- Language detection
    detected_language VARCHAR(10),
    language_confidence FLOAT,

    -- Topic classification
    primary_topic VARCHAR(100),
    topic_confidence FLOAT,
    topic_hierarchy JSONB, -- Hierarchical topic classification

    -- Named entity recognition
    entities JSONB, -- {type: [entities]} format
    entity_count INTEGER,

    -- Keyword extraction
    keywords JSONB, -- [{keyword, score, frequency}]
    key_phrases JSONB,

    -- Sentiment analysis
    sentiment_polarity FLOAT, -- -1 to 1
    sentiment_subjectivity FLOAT, -- 0 to 1
    emotion_scores JSONB, -- joy, anger, fear, etc.

    -- Content quality metrics
    readability_score FLOAT,
    grammar_score FLOAT,
    coherence_score FLOAT,

    -- Content structure analysis
    paragraph_count INTEGER,
    sentence_count INTEGER,
    avg_sentence_length FLOAT,
    complexity_score FLOAT,

    -- Processing metadata
    model_version VARCHAR(50),
    processing_time_ms INTEGER,
    processed_at TIMESTAMP DEFAULT NOW(),

    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_nlp_analysis_page_id ON nlp_analysis(page_id);
CREATE INDEX idx_nlp_analysis_primary_topic ON nlp_analysis(primary_topic);
CREATE INDEX idx_nlp_analysis_language ON nlp_analysis(detected_language);
CREATE INDEX idx_nlp_analysis_sentiment ON nlp_analysis(sentiment_polarity);

-- Topic taxonomy for hierarchical classification
CREATE TABLE topic_taxonomy (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    topic_name VARCHAR(100) UNIQUE NOT NULL,
    parent_topic_id UUID REFERENCES topic_taxonomy(id),
    level INTEGER NOT NULL, -- 0=root, 1=category, 2=subcategory, etc.
    description TEXT,
    keywords TEXT[],

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_topic_taxonomy_parent ON topic_taxonomy(parent_topic_id);
CREATE INDEX idx_topic_taxonomy_level ON topic_taxonomy(level);
```

**NLP Service Implementation**:
```python
# services/nlp/content_analyzer.py
import spacy
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
import asyncio
from typing import Dict, List, Optional
import json
from datetime import datetime

class ContentAnalyzer:
    """Advanced NLP content analysis service"""

    def __init__(self):
        # Load models
        self.nlp = spacy.load("en_core_web_lg")

        # Sentiment analysis
        self.sentiment_analyzer = pipeline(
            "sentiment-analysis",
            model="cardiffnlp/twitter-roberta-base-sentiment-latest"
        )

        # Topic classification
        self.topic_classifier = pipeline(
            "zero-shot-classification",
            model="facebook/bart-large-mnli"
        )

        # Language detection
        self.language_detector = pipeline(
            "text-classification",
            model="papluca/xlm-roberta-base-language-detection"
        )

        # Predefined topic categories
        self.topic_categories = [
            "Technology", "Business", "Science", "Health", "Politics",
            "Entertainment", "Sports", "Education", "Travel", "Food",
            "Fashion", "Finance", "Real Estate", "Automotive", "Gaming",
            "Art", "Music", "Literature", "News", "Opinion", "Tutorial",
            "Product Review", "Academic", "Legal", "Medical", "Research"
        ]

    async def analyze_content(self, page_data: Dict) -> Dict:
        """Perform comprehensive NLP analysis on page content"""
        content_text = page_data.get('content_text', '')

        if not content_text or len(content_text.strip()) < 50:
            return {'error': 'Insufficient content for analysis'}

        start_time = datetime.now()

        # Run analyses in parallel
        tasks = [
            self._detect_language(content_text),
            self._classify_topics(content_text),
            self._extract_entities(content_text),
            self._analyze_sentiment(content_text),
            self._extract_keywords(content_text),
            self._analyze_readability(content_text),
            self._analyze_structure(content_text)
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        end_time = datetime.now()
        processing_time = int((end_time - start_time).total_seconds() * 1000)

        # Combine results
        analysis = {
            'language_analysis': results[0] if not isinstance(results[0], Exception) else {},
            'topic_analysis': results[1] if not isinstance(results[1], Exception) else {},
            'entity_analysis': results[2] if not isinstance(results[2], Exception) else {},
            'sentiment_analysis': results[3] if not isinstance(results[3], Exception) else {},
            'keyword_analysis': results[4] if not isinstance(results[4], Exception) else {},
            'readability_analysis': results[5] if not isinstance(results[5], Exception) else {},
            'structure_analysis': results[6] if not isinstance(results[6], Exception) else {},
            'processing_time_ms': processing_time,
            'model_version': 'v1.0',
            'processed_at': datetime.utcnow().isoformat()
        }

        return analysis

    async def _detect_language(self, text: str) -> Dict:
        """Detect content language"""
        try:
            # Use first 512 characters for language detection
            sample_text = text[:512]
            result = self.language_detector(sample_text)

            return {
                'detected_language': result[0]['label'],
                'confidence': result[0]['score']
            }
        except Exception as e:
            return {'error': str(e)}

    async def _classify_topics(self, text: str) -> Dict:
        """Classify content topics using zero-shot classification"""
        try:
            # Use first 1000 characters for topic classification
            sample_text = text[:1000]
            result = self.topic_classifier(sample_text, self.topic_categories)

            # Create hierarchical topic structure
            primary_topic = result['labels'][0]
            topic_scores = dict(zip(result['labels'], result['scores']))

            return {
                'primary_topic': primary_topic,
                'topic_confidence': result['scores'][0],
                'all_topics': topic_scores,
                'topic_hierarchy': self._build_topic_hierarchy(topic_scores)
            }
        except Exception as e:
            return {'error': str(e)}

    async def _extract_entities(self, text: str) -> Dict:
        """Extract named entities using spaCy"""
        try:
            doc = self.nlp(text[:5000])  # Limit to first 5000 chars

            entities = {}
            for ent in doc.ents:
                entity_type = ent.label_
                entity_text = ent.text.strip()

                if entity_type not in entities:
                    entities[entity_type] = []

                if entity_text not in entities[entity_type]:
                    entities[entity_type].append(entity_text)

            return {
                'entities': entities,
                'entity_count': sum(len(ents) for ents in entities.values())
            }
        except Exception as e:
            return {'error': str(e)}

    async def _analyze_sentiment(self, text: str) -> Dict:
        """Analyze sentiment and emotions"""
        try:
            # Use first 512 characters for sentiment analysis
            sample_text = text[:512]
            result = self.sentiment_analyzer(sample_text)

            # Convert to polarity score (-1 to 1)
            label = result[0]['label'].lower()
            score = result[0]['score']

            if 'positive' in label:
                polarity = score
            elif 'negative' in label:
                polarity = -score
            else:
                polarity = 0.0

            return {
                'sentiment_polarity': polarity,
                'sentiment_label': label,
                'confidence': score
            }
        except Exception as e:
            return {'error': str(e)}

    async def _extract_keywords(self, text: str) -> Dict:
        """Extract keywords and key phrases"""
        try:
            doc = self.nlp(text[:3000])  # Limit to first 3000 chars

            # Extract keywords based on POS tags and frequency
            keywords = {}
            for token in doc:
                if (token.pos_ in ['NOUN', 'ADJ', 'VERB'] and
                    not token.is_stop and
                    not token.is_punct and
                    len(token.text) > 2):

                    lemma = token.lemma_.lower()
                    keywords[lemma] = keywords.get(lemma, 0) + 1

            # Sort by frequency and get top keywords
            sorted_keywords = sorted(keywords.items(), key=lambda x: x[1], reverse=True)
            top_keywords = [
                {'keyword': kw, 'frequency': freq, 'score': freq / len(doc)}
                for kw, freq in sorted_keywords[:20]
            ]

            return {
                'keywords': top_keywords,
                'total_keywords': len(keywords)
            }
        except Exception as e:
            return {'error': str(e)}

    async def _analyze_readability(self, text: str) -> Dict:
        """Analyze text readability and complexity"""
        try:
            sentences = text.split('.')
            words = text.split()

            if not sentences or not words:
                return {'readability_score': 0.0}

            # Simple readability metrics
            avg_sentence_length = len(words) / len(sentences)
            avg_word_length = sum(len(word) for word in words) / len(words)

            # Flesch Reading Ease approximation
            readability_score = 206.835 - (1.015 * avg_sentence_length) - (84.6 * avg_word_length / 4.7)
            readability_score = max(0, min(100, readability_score)) / 100  # Normalize to 0-1

            return {
                'readability_score': readability_score,
                'avg_sentence_length': avg_sentence_length,
                'avg_word_length': avg_word_length
            }
        except Exception as e:
            return {'error': str(e)}

    async def _analyze_structure(self, text: str) -> Dict:
        """Analyze content structure"""
        try:
            paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
            sentences = [s.strip() for s in text.split('.') if s.strip()]
            words = text.split()

            return {
                'paragraph_count': len(paragraphs),
                'sentence_count': len(sentences),
                'word_count': len(words),
                'avg_paragraph_length': len(words) / len(paragraphs) if paragraphs else 0,
                'avg_sentence_length': len(words) / len(sentences) if sentences else 0
            }
        except Exception as e:
            return {'error': str(e)}

    def _build_topic_hierarchy(self, topic_scores: Dict) -> Dict:
        """Build hierarchical topic structure"""
        # Simplified hierarchy - in production, use a proper taxonomy
        hierarchy = {
            'primary': max(topic_scores.items(), key=lambda x: x[1]),
            'secondary': sorted(topic_scores.items(), key=lambda x: x[1], reverse=True)[1:3],
            'confidence_threshold': 0.5
        }
        return hierarchy
```

### **Milestone 2.2: Entity Recognition & Knowledge Extraction** ------------> complete

**Objective**: Extract and catalog entities, relationships, and knowledge from content

**Database Schema Extensions**:
```sql
-- Entity storage and relationships
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(500) NOT NULL,
    entity_type VARCHAR(50) NOT NULL, -- PERSON, ORG, GPE, PRODUCT, etc.
    canonical_name VARCHAR(500), -- Normalized name for deduplication
    description TEXT,

    -- Entity metadata
    confidence_score FLOAT,
    frequency_count INTEGER DEFAULT 1,
    first_mentioned TIMESTAMP DEFAULT NOW(),
    last_mentioned TIMESTAMP DEFAULT NOW(),

    -- External identifiers
    wikipedia_id VARCHAR(100),
    wikidata_id VARCHAR(100),
    external_ids JSONB,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_entities_canonical ON entities(canonical_name, entity_type);
CREATE INDEX idx_entities_type ON entities(entity_type);
CREATE INDEX idx_entities_frequency ON entities(frequency_count DESC);

-- Page-entity relationships
CREATE TABLE page_entities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    page_id UUID REFERENCES pages(id) ON DELETE CASCADE,
    entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,

    -- Context information
    mention_text VARCHAR(500), -- Actual text as mentioned
    context_before TEXT, -- 100 chars before mention
    context_after TEXT, -- 100 chars after mention
    position_in_text INTEGER, -- Character position

    -- Relationship metadata
    confidence_score FLOAT,
    mention_type VARCHAR(20), -- direct, indirect, reference

    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_page_entities_page ON page_entities(page_id);
CREATE INDEX idx_page_entities_entity ON page_entities(entity_id);
CREATE UNIQUE INDEX idx_page_entities_unique ON page_entities(page_id, entity_id, position_in_text);

-- Entity relationships (who mentions whom, co-occurrences)
CREATE TABLE entity_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity1_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    entity2_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50), -- co_occurrence, mentions, works_at, etc.

    -- Relationship strength
    co_occurrence_count INTEGER DEFAULT 1,
    relationship_strength FLOAT,

    -- Context
    first_seen TIMESTAMP DEFAULT NOW(),
    last_seen TIMESTAMP DEFAULT NOW(),
    example_contexts JSONB, -- Array of example contexts

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_entity_relationships_entity1 ON entity_relationships(entity1_id);
CREATE INDEX idx_entity_relationships_entity2 ON entity_relationships(entity2_id);
CREATE INDEX idx_entity_relationships_type ON entity_relationships(relationship_type);
```

**Entity Extraction Service**:
```python
# services/nlp/entity_extractor.py
import spacy
from spacy import displacy
from collections import defaultdict, Counter
import re
from typing import Dict, List, Tuple, Set
from datetime import datetime
import logging

class EntityExtractor:
    """Advanced entity extraction and relationship mapping"""

    def __init__(self, session):
        self.session = session
        self.nlp = spacy.load("en_core_web_lg")

        # Add custom entity patterns if needed
        self.entity_cache = {}  # Cache for entity normalization

        # Entity type mapping
        self.entity_type_mapping = {
            'PERSON': 'PERSON',
            'ORG': 'ORGANIZATION',
            'GPE': 'LOCATION',
            'PRODUCT': 'PRODUCT',
            'EVENT': 'EVENT',
            'WORK_OF_ART': 'CREATIVE_WORK',
            'LAW': 'LEGAL_DOCUMENT',
            'LANGUAGE': 'LANGUAGE',
            'DATE': 'DATE',
            'TIME': 'TIME',
            'PERCENT': 'PERCENTAGE',
            'MONEY': 'MONETARY_VALUE',
            'QUANTITY': 'QUANTITY',
            'ORDINAL': 'ORDINAL',
            'CARDINAL': 'CARDINAL'
        }

    async def extract_entities(self, page_data: Dict) -> Dict:
        """Extract entities and relationships from page content"""
        content_text = page_data.get('content_text', '')
        page_id = page_data.get('page_id')

        if not content_text or len(content_text.strip()) < 50:
            return {'error': 'Insufficient content for entity extraction'}

        try:
            # Process text with spaCy
            doc = self.nlp(content_text[:10000])  # Limit to first 10k chars

            # Extract entities
            entities = self._extract_entities_from_doc(doc)

            # Find entity relationships
            relationships = self._find_entity_relationships(doc, entities)

            # Store entities and relationships
            stored_entities = await self._store_entities(entities, page_id)
            stored_relationships = await self._store_relationships(relationships)

            return {
                'entities_found': len(entities),
                'relationships_found': len(relationships),
                'entities': stored_entities,
                'relationships': stored_relationships,
                'processing_successful': True
            }

        except Exception as e:
            logging.error(f"Entity extraction failed for page {page_id}: {e}")
            return {'error': str(e), 'processing_successful': False}

    def _extract_entities_from_doc(self, doc) -> List[Dict]:
        """Extract entities with context from spaCy doc"""
        entities = []

        for ent in doc.ents:
            # Skip very short or common entities
            if len(ent.text.strip()) < 2 or ent.text.lower() in ['the', 'and', 'or']:
                continue

            # Get context around entity
            start_char = max(0, ent.start_char - 100)
            end_char = min(len(doc.text), ent.end_char + 100)
            context_before = doc.text[start_char:ent.start_char].strip()
            context_after = doc.text[ent.end_char:end_char].strip()

            entity_data = {
                'text': ent.text.strip(),
                'label': self.entity_type_mapping.get(ent.label_, ent.label_),
                'start_char': ent.start_char,
                'end_char': ent.end_char,
                'context_before': context_before[-100:],  # Last 100 chars
                'context_after': context_after[:100],     # First 100 chars
                'confidence': getattr(ent, 'confidence', 0.8),  # Default confidence
                'canonical_name': self._normalize_entity_name(ent.text, ent.label_)
            }

            entities.append(entity_data)

        return entities

    def _normalize_entity_name(self, entity_text: str, entity_type: str) -> str:
        """Normalize entity names for deduplication"""
        # Basic normalization - can be enhanced with external APIs
        normalized = entity_text.strip().title()

        # Remove common prefixes/suffixes for organizations
        if entity_type in ['ORG', 'ORGANIZATION']:
            normalized = re.sub(r'\b(Inc|LLC|Corp|Ltd|Co)\b\.?$', '', normalized, flags=re.IGNORECASE)

        # Remove titles for persons
        if entity_type == 'PERSON':
            normalized = re.sub(r'^(Mr|Mrs|Ms|Dr|Prof|Sir|Dame)\.?\s+', '', normalized, flags=re.IGNORECASE)

        return normalized.strip()

    def _find_entity_relationships(self, doc, entities: List[Dict]) -> List[Dict]:
        """Find relationships between entities based on co-occurrence"""
        relationships = []

        # Group entities by sentence for co-occurrence analysis
        sentence_entities = defaultdict(list)

        for sent in doc.sents:
            sent_entities = []
            for entity in entities:
                if sent.start_char <= entity['start_char'] < sent.end_char:
                    sent_entities.append(entity)

            if len(sent_entities) >= 2:
                sentence_entities[sent.text] = sent_entities

        # Find co-occurrences within sentences
        for sentence, sent_entities in sentence_entities.items():
            for i, entity1 in enumerate(sent_entities):
                for entity2 in sent_entities[i+1:]:
                    relationship = {
                        'entity1': entity1['canonical_name'],
                        'entity1_type': entity1['label'],
                        'entity2': entity2['canonical_name'],
                        'entity2_type': entity2['label'],
                        'relationship_type': 'co_occurrence',
                        'context': sentence[:500],  # First 500 chars of sentence
                        'confidence': min(entity1['confidence'], entity2['confidence'])
                    }
                    relationships.append(relationship)

        return relationships

    async def _store_entities(self, entities: List[Dict], page_id: str) -> List[Dict]:
        """Store entities in database with deduplication"""
        from models.entities import Entity, PageEntity

        stored_entities = []

        for entity_data in entities:
            # Check if entity already exists
            existing_entity = self.session.query(Entity).filter_by(
                canonical_name=entity_data['canonical_name'],
                entity_type=entity_data['label']
            ).first()

            if existing_entity:
                # Update frequency and last mentioned
                existing_entity.frequency_count += 1
                existing_entity.last_mentioned = datetime.utcnow()
                entity = existing_entity
            else:
                # Create new entity
                entity = Entity(
                    name=entity_data['text'],
                    entity_type=entity_data['label'],
                    canonical_name=entity_data['canonical_name'],
                    confidence_score=entity_data['confidence'],
                    frequency_count=1
                )
                self.session.add(entity)
                self.session.flush()  # Get the ID

            # Create page-entity relationship
            page_entity = PageEntity(
                page_id=page_id,
                entity_id=entity.id,
                mention_text=entity_data['text'],
                context_before=entity_data['context_before'],
                context_after=entity_data['context_after'],
                position_in_text=entity_data['start_char'],
                confidence_score=entity_data['confidence'],
                mention_type='direct'
            )
            self.session.add(page_entity)

            stored_entities.append({
                'id': str(entity.id),
                'name': entity.name,
                'type': entity.entity_type,
                'canonical_name': entity.canonical_name
            })

        self.session.commit()
        return stored_entities

    async def _store_relationships(self, relationships: List[Dict]) -> List[Dict]:
        """Store entity relationships with deduplication"""
        from models.entities import Entity, EntityRelationship

        stored_relationships = []

        for rel_data in relationships:
            # Find entities
            entity1 = self.session.query(Entity).filter_by(
                canonical_name=rel_data['entity1'],
                entity_type=rel_data['entity1_type']
            ).first()

            entity2 = self.session.query(Entity).filter_by(
                canonical_name=rel_data['entity2'],
                entity_type=rel_data['entity2_type']
            ).first()

            if not entity1 or not entity2:
                continue

            # Check if relationship already exists
            existing_rel = self.session.query(EntityRelationship).filter_by(
                entity1_id=entity1.id,
                entity2_id=entity2.id,
                relationship_type=rel_data['relationship_type']
            ).first()

            if existing_rel:
                # Update co-occurrence count
                existing_rel.co_occurrence_count += 1
                existing_rel.last_seen = datetime.utcnow()

                # Add context to examples (limit to 10 examples)
                examples = existing_rel.example_contexts or []
                if len(examples) < 10:
                    examples.append(rel_data['context'])
                    existing_rel.example_contexts = examples

                relationship = existing_rel
            else:
                # Create new relationship
                relationship = EntityRelationship(
                    entity1_id=entity1.id,
                    entity2_id=entity2.id,
                    relationship_type=rel_data['relationship_type'],
                    co_occurrence_count=1,
                    relationship_strength=rel_data['confidence'],
                    example_contexts=[rel_data['context']]
                )
                self.session.add(relationship)

            stored_relationships.append({
                'entity1': rel_data['entity1'],
                'entity2': rel_data['entity2'],
                'type': rel_data['relationship_type'],
                'strength': relationship.relationship_strength
            })

        self.session.commit()
        return stored_relationships
```

### **Milestone 2.3: Testing & Quality Assurance Framework**

**Objective**: Implement comprehensive testing, CI/CD pipeline, and code quality gates

**Testing Infrastructure**:
```python
# tests/conftest.py
import pytest
import asyncio
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models.base import Base
from services.crawler.content_scraper import EnhancedContentScraper
from services.nlp.content_analyzer import ContentAnalyzer

@pytest.fixture(scope="session")
def test_database():
    """Create test database for integration tests"""
    engine = create_engine("postgresql://test_user:test_pass@localhost/test_web_crawler")
    Base.metadata.create_all(engine)
    yield engine
    Base.metadata.drop_all(engine)

@pytest.fixture
def db_session(test_database):
    """Create database session for tests"""
    Session = sessionmaker(bind=test_database)
    session = Session()
    yield session
    session.rollback()
    session.close()

@pytest.fixture
def sample_html():
    """Sample HTML content for testing"""
    return """
    <html>
        <head><title>Test Page</title></head>
        <body>
            <h1>Welcome to Test Corp</h1>
            <p>John Smith is the CEO of Test Corp, located in New York.</p>
            <a href="https://example.com">External Link</a>
        </body>
    </html>
    """

# tests/test_content_scraper.py
import pytest
from unittest.mock import AsyncMock, patch
from services.crawler.content_scraper import EnhancedContentScraper

class TestEnhancedContentScraper:

    @pytest.mark.asyncio
    async def test_extract_page_data(self, sample_html):
        """Test page data extraction"""
        scraper = EnhancedContentScraper(None, None, None)

        with patch('aiohttp.ClientResponse') as mock_response:
            mock_response.status = 200
            mock_response.headers = {'content-type': 'text/html'}

            result = await scraper._extract_page_data(
                "https://test.com", sample_html, mock_response
            )

            assert result['title'] == 'Test Page'
            assert 'John Smith' in result['content_text']
            assert result['http_status'] == 200
            assert len(result['links']) > 0

    @pytest.mark.asyncio
    async def test_robots_compliance(self):
        """Test robots.txt compliance checking"""
        # Implementation for robots.txt testing
        pass

    @pytest.mark.asyncio
    async def test_rate_limiting(self):
        """Test rate limiting functionality"""
        # Implementation for rate limiting testing
        pass

# tests/test_nlp_analyzer.py
import pytest
from services.nlp.content_analyzer import ContentAnalyzer

class TestContentAnalyzer:

    @pytest.mark.asyncio
    async def test_language_detection(self):
        """Test language detection accuracy"""
        analyzer = ContentAnalyzer()

        english_text = "This is a sample English text for testing."
        result = await analyzer._detect_language(english_text)

        assert result['detected_language'] in ['en', 'english']
        assert result['confidence'] > 0.8

    @pytest.mark.asyncio
    async def test_entity_extraction(self):
        """Test named entity recognition"""
        analyzer = ContentAnalyzer()

        text = "Apple Inc. was founded by Steve Jobs in Cupertino, California."
        result = await analyzer._extract_entities(text)

        assert 'ORG' in result['entities']
        assert 'PERSON' in result['entities']
        assert 'GPE' in result['entities']

    @pytest.mark.asyncio
    async def test_sentiment_analysis(self):
        """Test sentiment analysis accuracy"""
        analyzer = ContentAnalyzer()

        positive_text = "This is an amazing product that I absolutely love!"
        negative_text = "This is terrible and I hate it completely."

        pos_result = await analyzer._analyze_sentiment(positive_text)
        neg_result = await analyzer._analyze_sentiment(negative_text)

        assert pos_result['sentiment_polarity'] > 0
        assert neg_result['sentiment_polarity'] < 0
```

**CI/CD Pipeline Configuration**:
```yaml
# .github/workflows/ci.yml
name: Web Crawler CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_pass
          POSTGRES_USER: test_user
          POSTGRES_DB: test_web_crawler
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: 3.12

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Run code quality checks
      run: |
        black --check .
        flake8 .
        mypy .
        bandit -r . -x tests/

    - name: Run tests
      run: |
        pytest tests/ -v --cov=. --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Run security scan
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'security-scan-results.sarif'
```

**Success Criteria for Phase 2**:
- [ ] NLP analysis pipeline processing 500+ pages/hour
- [ ] Entity extraction with 95%+ accuracy on standard datasets
- [ ] Topic classification with 90%+ precision across 25+ categories
- [ ] Sentiment analysis with 85%+ accuracy
- [ ] Comprehensive test suite with 95%+ code coverage
- [ ] Automated CI/CD pipeline with quality gates
- [ ] Security scanning and vulnerability assessment
- [ ] GDPR compliance framework operational
- [ ] Performance monitoring and alerting systems
- [ ] Database storing 100,000+ analyzed pages with relationships

---

## 🔍 **PHASE 3: SEARCH & API INFRASTRUCTURE**
**Priority**: High |

### **Milestone 3.1: Vector Database & Semantic Search**

**Objective**: Implement semantic search capabilities with vector embeddings and similarity matching

**Database Schema for Vector Storage**:
```sql
-- Vector embeddings storage with pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

CREATE TABLE page_vectors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    page_id UUID REFERENCES pages(id) ON DELETE CASCADE,

    -- Vector embeddings
    content_embedding VECTOR(768), -- Full content embedding
    title_embedding VECTOR(768),   -- Title-specific embedding
    summary_embedding VECTOR(384), -- Summary embedding (smaller model)

    -- Embedding metadata
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    embedding_created_at TIMESTAMP DEFAULT NOW(),

    -- Content metadata for search optimization
    content_length INTEGER,
    language VARCHAR(10),
    content_hash VARCHAR(64),

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for vector similarity search
CREATE INDEX idx_page_vectors_content_embedding ON page_vectors
USING ivfflat (content_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX idx_page_vectors_title_embedding ON page_vectors
USING ivfflat (title_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX idx_page_vectors_page_id ON page_vectors(page_id);
CREATE INDEX idx_page_vectors_language ON page_vectors(language);
CREATE INDEX idx_page_vectors_model ON page_vectors(model_name, model_version);

-- Search query history for analytics
CREATE TABLE search_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_text TEXT NOT NULL,
    query_embedding VECTOR(768),

    -- Search parameters
    search_type VARCHAR(20), -- semantic, hybrid, keyword
    limit_requested INTEGER,
    filters_applied JSONB,

    -- Results metadata
    results_count INTEGER,
    response_time_ms INTEGER,

    -- User context (if applicable)
    user_id VARCHAR(100),
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,

    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX idx_search_queries_query_text ON search_queries(query_text);
CREATE INDEX idx_search_queries_user_id ON search_queries(user_id);
```

**Vector Embedding Service Implementation**:
```python
# services/search/embedding_service.py
from sentence_transformers import SentenceTransformer
import numpy as np
from typing import List, Dict, Optional, Tuple
import asyncio
import logging
from datetime import datetime
import hashlib

class EmbeddingService:
    """Generate and manage vector embeddings for semantic search"""

    def __init__(self, session):
        self.session = session
        self.logger = logging.getLogger(__name__)

        # Load embedding models
        self.content_model = SentenceTransformer('all-MiniLM-L6-v2')  # 384 dim
        self.title_model = SentenceTransformer('all-MiniLM-L6-v2')    # 384 dim
        self.large_model = SentenceTransformer('all-mpnet-base-v2')   # 768 dim

        # Model metadata
        self.model_info = {
            'content_model': {
                'name': 'all-MiniLM-L6-v2',
                'version': '1.0',
                'dimensions': 384
            },
            'large_model': {
                'name': 'all-mpnet-base-v2',
                'version': '1.0',
                'dimensions': 768
            }
        }

    async def generate_page_embeddings(self, page_data: Dict) -> Dict:
        """Generate embeddings for page content, title, and summary"""
        try:
            page_id = page_data.get('page_id')
            content_text = page_data.get('content_text', '')
            title = page_data.get('title', '')
            summary = page_data.get('summary', '')

            if not content_text or len(content_text.strip()) < 50:
                return {'error': 'Insufficient content for embedding generation'}

            # Prepare texts for embedding
            content_for_embedding = self._prepare_text_for_embedding(content_text, max_length=5000)
            title_for_embedding = self._prepare_text_for_embedding(title, max_length=500)
            summary_for_embedding = self._prepare_text_for_embedding(summary, max_length=1000)

            # Generate embeddings in parallel
            tasks = [
                self._generate_content_embedding(content_for_embedding),
                self._generate_title_embedding(title_for_embedding),
                self._generate_summary_embedding(summary_for_embedding)
            ]

            embeddings = await asyncio.gather(*tasks, return_exceptions=True)

            # Store embeddings in database
            embedding_data = {
                'page_id': page_id,
                'content_embedding': embeddings[0] if not isinstance(embeddings[0], Exception) else None,
                'title_embedding': embeddings[1] if not isinstance(embeddings[1], Exception) else None,
                'summary_embedding': embeddings[2] if not isinstance(embeddings[2], Exception) else None,
                'content_length': len(content_text),
                'language': page_data.get('detected_language', 'en'),
                'content_hash': hashlib.sha256(content_text.encode()).hexdigest()
            }

            stored_embedding = await self._store_embeddings(embedding_data)

            return {
                'success': True,
                'embedding_id': stored_embedding['id'],
                'dimensions': {
                    'content': len(embeddings[0]) if embeddings[0] is not None else 0,
                    'title': len(embeddings[1]) if embeddings[1] is not None else 0,
                    'summary': len(embeddings[2]) if embeddings[2] is not None else 0
                }
            }

        except Exception as e:
            self.logger.error(f"Embedding generation failed for page {page_id}: {e}")
            return {'error': str(e), 'success': False}

    async def search_similar_content(self, query: str, limit: int = 10,
                                   similarity_threshold: float = 0.7) -> List[Dict]:
        """Search for similar content using vector similarity"""
        try:
            # Generate query embedding
            query_embedding = self.large_model.encode(query, convert_to_tensor=False)

            # Perform vector similarity search using pgvector
            from sqlalchemy import text

            similarity_query = text("""
                SELECT
                    pv.page_id,
                    p.url,
                    p.title,
                    p.meta_description,
                    1 - (pv.content_embedding <=> :query_embedding) as similarity_score
                FROM page_vectors pv
                JOIN pages p ON pv.page_id = p.id
                WHERE 1 - (pv.content_embedding <=> :query_embedding) > :threshold
                ORDER BY pv.content_embedding <=> :query_embedding
                LIMIT :limit
            """)

            results = self.session.execute(similarity_query, {
                'query_embedding': query_embedding.tolist(),
                'threshold': similarity_threshold,
                'limit': limit
            }).fetchall()

            # Format and return results
            return [
                {
                    'page_id': str(result.page_id),
                    'url': result.url,
                    'title': result.title,
                    'description': result.meta_description,
                    'similarity_score': float(result.similarity_score)
                }
                for result in results
            ]

        except Exception as e:
            self.logger.error(f"Vector search failed: {e}")
            return []
```

### **Milestone 3.2: FastAPI REST API Development**

**Objective**: Create comprehensive REST API with OpenAPI documentation and authentication

**API Architecture**:
```python
# api/main.py
from fastapi import FastAPI, HTTPException, Depends, Security, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging
from datetime import datetime

# Initialize FastAPI with comprehensive metadata
app = FastAPI(
    title="Universal Web Directory API",
    description="""
    A comprehensive web intelligence API providing access to crawled web data,
    semantic search capabilities, and content analysis insights.

    ## Features
    * **Search**: Semantic and keyword-based search across millions of web pages
    * **Content Analysis**: NLP-powered content classification and entity extraction
    * **Site Intelligence**: Domain-level insights and authority scoring
    * **Real-time Data**: Fresh content with automated crawling and updates
    """,
    version="1.0.0",
    contact={
        "name": "Universal Web Directory Team",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    docs_url="/docs",
    redoc_url="/redoc"
)

# Security and middleware
security = HTTPBearer()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://universalwebdirectory.com", "https://api.universalwebdirectory.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["universalwebdirectory.com", "*.universalwebdirectory.com", "localhost"]
)

# Request/Response Models
class SearchRequest(BaseModel):
    """Search request model with validation"""
    query: str = Field(..., min_length=1, max_length=500, description="Search query")
    search_type: str = Field("hybrid", regex="^(semantic|keyword|hybrid)$", description="Type of search")
    limit: int = Field(10, ge=1, le=100, description="Number of results to return")
    offset: int = Field(0, ge=0, description="Pagination offset")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional search filters")

    class Config:
        schema_extra = {
            "example": {
                "query": "artificial intelligence machine learning",
                "search_type": "hybrid",
                "limit": 20,
                "offset": 0,
                "filters": {
                    "language": "en",
                    "domain_authority": {"min": 50},
                    "content_type": ["article", "blog"]
                }
            }
        }

class SearchResult(BaseModel):
    """Individual search result model"""
    page_id: str
    url: str
    title: str
    description: Optional[str]
    similarity_score: Optional[float] = Field(None, ge=0, le=1)
    domain: str
    content_type: Optional[str]
    language: Optional[str]
    last_crawled: datetime
    authority_score: Optional[float]

class SearchResponse(BaseModel):
    """Search response model"""
    query: str
    search_type: str
    total_results: int
    results: List[SearchResult]
    response_time_ms: int
    pagination: Dict[str, Any]

class SiteAnalysis(BaseModel):
    """Site analysis response model"""
    domain: str
    title: Optional[str]
    description: Optional[str]
    authority_score: Optional[float]
    page_count: int
    last_crawled: Optional[datetime]
    technology_stack: Optional[Dict[str, Any]]
    content_categories: List[str]
    language_distribution: Dict[str, int]

# Authentication dependency
async def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)):
    """Verify API key authentication"""
    from services.auth.api_auth import APIAuthService

    auth_service = APIAuthService()
    api_key = credentials.credentials

    if not await auth_service.verify_api_key(api_key):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return api_key

# API Routes
@app.get("/", tags=["Health"])
async def root():
    """API health check and basic information"""
    return {
        "service": "Universal Web Directory API",
        "version": "1.0.0",
        "status": "operational",
        "timestamp": datetime.utcnow().isoformat(),
        "documentation": "/docs"
    }

@app.post("/search", response_model=SearchResponse, tags=["Search"])
async def search_content(
    request: SearchRequest,
    api_key: str = Depends(verify_api_key)
):
    """
    Perform semantic or keyword search across the web directory

    - **query**: Search terms or natural language query
    - **search_type**: Choose between semantic, keyword, or hybrid search
    - **limit**: Number of results (1-100)
    - **offset**: Pagination offset
    - **filters**: Additional filters for domain, language, content type, etc.
    """
    from services.search.search_service import SearchService

    start_time = datetime.now()

    try:
        search_service = SearchService()
        results = await search_service.search(
            query=request.query,
            search_type=request.search_type,
            limit=request.limit,
            offset=request.offset,
            filters=request.filters
        )

        end_time = datetime.now()
        response_time = int((end_time - start_time).total_seconds() * 1000)

        return SearchResponse(
            query=request.query,
            search_type=request.search_type,
            total_results=results['total_count'],
            results=[SearchResult(**result) for result in results['items']],
            response_time_ms=response_time,
            pagination={
                "limit": request.limit,
                "offset": request.offset,
                "has_next": results['has_next'],
                "total_pages": results['total_pages']
            }
        )

    except Exception as e:
        logging.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail="Search service temporarily unavailable")

@app.get("/sites/{domain}/analysis", response_model=SiteAnalysis, tags=["Site Intelligence"])
async def get_site_analysis(
    domain: str,
    api_key: str = Depends(verify_api_key)
):
    """
    Get comprehensive analysis for a specific domain

    Returns domain-level insights including:
    - Authority and trust metrics
    - Content categorization
    - Technology stack detection
    - Language distribution
    - Crawling statistics
    """
    from services.analysis.site_analyzer import SiteAnalyzer

    try:
        analyzer = SiteAnalyzer()
        analysis = await analyzer.analyze_domain(domain)

        if not analysis:
            raise HTTPException(status_code=404, detail="Domain not found in directory")

        return SiteAnalysis(**analysis)

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Site analysis failed for {domain}: {e}")
        raise HTTPException(status_code=500, detail="Analysis service temporarily unavailable")

@app.get("/content/{page_id}", tags=["Content"])
async def get_page_content(
    page_id: str,
    include_analysis: bool = False,
    api_key: str = Depends(verify_api_key)
):
    """
    Retrieve detailed content and metadata for a specific page

    - **page_id**: Unique identifier for the page
    - **include_analysis**: Include NLP analysis results (entities, topics, sentiment)
    """
    from services.content.content_service import ContentService

    try:
        content_service = ContentService()
        content = await content_service.get_page_details(page_id, include_analysis)

        if not content:
            raise HTTPException(status_code=404, detail="Page not found")

        return content

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Content retrieval failed for page {page_id}: {e}")
        raise HTTPException(status_code=500, detail="Content service temporarily unavailable")

@app.get("/trending", tags=["Intelligence"])
async def get_trending_topics(
    timeframe: str = "24h",
    limit: int = 20,
    api_key: str = Depends(verify_api_key)
):
    """
    Get trending topics and emerging content patterns

    - **timeframe**: Time period for trend analysis (1h, 24h, 7d, 30d)
    - **limit**: Number of trending topics to return
    """
    from services.intelligence.trend_analyzer import TrendAnalyzer

    try:
        trend_analyzer = TrendAnalyzer()
        trends = await trend_analyzer.get_trending_topics(timeframe, limit)

        return {
            "timeframe": timeframe,
            "trending_topics": trends,
            "generated_at": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logging.error(f"Trend analysis failed: {e}")
        raise HTTPException(status_code=500, detail="Trend analysis service temporarily unavailable")

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logging.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "status_code": 500,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
```

### **Milestone 3.3: Observability & Monitoring Framework**

**Objective**: Implement comprehensive logging, metrics, tracing, and alerting systems

**Monitoring Infrastructure**:
```python
# services/monitoring/metrics_collector.py
import time
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque
import asyncio
import psutil
import threading

@dataclass
class MetricPoint:
    """Individual metric data point"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str]
    metric_type: str  # counter, gauge, histogram, timer

class MetricsCollector:
    """Collect and aggregate system and application metrics"""

    def __init__(self):
        self.metrics = defaultdict(deque)
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.timers = defaultdict(list)
        self.lock = threading.Lock()

        # System monitoring
        self.system_metrics_enabled = True
        self.collection_interval = 60  # seconds

        # Start background collection
        self._start_background_collection()

    def increment_counter(self, name: str, value: int = 1, tags: Dict[str, str] = None):
        """Increment a counter metric"""
        with self.lock:
            self.counters[name] += value
            self._record_metric(name, value, "counter", tags or {})

    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """Set a gauge metric value"""
        with self.lock:
            self.gauges[name] = value
            self._record_metric(name, value, "gauge", tags or {})

    def record_timer(self, name: str, duration_ms: float, tags: Dict[str, str] = None):
        """Record a timing metric"""
        with self.lock:
            self.timers[name].append(duration_ms)
            # Keep only last 1000 measurements
            if len(self.timers[name]) > 1000:
                self.timers[name] = self.timers[name][-1000:]
            self._record_metric(name, duration_ms, "timer", tags or {})

    def _record_metric(self, name: str, value: float, metric_type: str, tags: Dict[str, str]):
        """Record a metric point"""
        metric_point = MetricPoint(
            name=name,
            value=value,
            timestamp=datetime.utcnow(),
            tags=tags,
            metric_type=metric_type
        )

        self.metrics[name].append(metric_point)

        # Keep only last 10000 points per metric
        if len(self.metrics[name]) > 10000:
            self.metrics[name].popleft()

    def _start_background_collection(self):
        """Start background system metrics collection"""
        def collect_system_metrics():
            while self.system_metrics_enabled:
                try:
                    # CPU metrics
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.set_gauge("system.cpu.usage_percent", cpu_percent)

                    # Memory metrics
                    memory = psutil.virtual_memory()
                    self.set_gauge("system.memory.usage_percent", memory.percent)
                    self.set_gauge("system.memory.available_bytes", memory.available)

                    # Disk metrics
                    disk = psutil.disk_usage('/')
                    self.set_gauge("system.disk.usage_percent", disk.percent)
                    self.set_gauge("system.disk.free_bytes", disk.free)

                    # Network metrics
                    network = psutil.net_io_counters()
                    self.increment_counter("system.network.bytes_sent", network.bytes_sent)
                    self.increment_counter("system.network.bytes_recv", network.bytes_recv)

                    time.sleep(self.collection_interval)

                except Exception as e:
                    logging.error(f"System metrics collection failed: {e}")
                    time.sleep(self.collection_interval)

        thread = threading.Thread(target=collect_system_metrics, daemon=True)
        thread.start()

    def get_metrics_summary(self, timeframe_minutes: int = 60) -> Dict[str, Any]:
        """Get metrics summary for the specified timeframe"""
        cutoff_time = datetime.utcnow() - timedelta(minutes=timeframe_minutes)

        summary = {
            "timeframe_minutes": timeframe_minutes,
            "counters": {},
            "gauges": {},
            "timers": {},
            "generated_at": datetime.utcnow().isoformat()
        }

        with self.lock:
            # Process counters
            for name, points in self.metrics.items():
                recent_points = [p for p in points if p.timestamp > cutoff_time and p.metric_type == "counter"]
                if recent_points:
                    summary["counters"][name] = {
                        "total": sum(p.value for p in recent_points),
                        "count": len(recent_points),
                        "rate_per_minute": len(recent_points) / timeframe_minutes
                    }

            # Process gauges (latest values)
            for name, value in self.gauges.items():
                summary["gauges"][name] = {
                    "current_value": value,
                    "last_updated": datetime.utcnow().isoformat()
                }

            # Process timers
            for name, durations in self.timers.items():
                if durations:
                    recent_durations = durations[-100:]  # Last 100 measurements
                    summary["timers"][name] = {
                        "avg_ms": sum(recent_durations) / len(recent_durations),
                        "min_ms": min(recent_durations),
                        "max_ms": max(recent_durations),
                        "count": len(recent_durations)
                    }

        return summary

# Application performance monitoring
class PerformanceMonitor:
    """Monitor application performance and detect anomalies"""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.alert_thresholds = {
            "response_time_ms": 5000,  # 5 seconds
            "error_rate_percent": 5.0,  # 5%
            "cpu_usage_percent": 80.0,  # 80%
            "memory_usage_percent": 85.0,  # 85%
            "disk_usage_percent": 90.0   # 90%
        }

        self.alerts_sent = defaultdict(datetime)
        self.alert_cooldown_minutes = 15

    async def check_performance_alerts(self):
        """Check for performance issues and send alerts"""
        alerts = []

        # Get recent metrics
        summary = self.metrics.get_metrics_summary(timeframe_minutes=5)

        # Check response time alerts
        for timer_name, timer_data in summary.get("timers", {}).items():
            if "response_time" in timer_name and timer_data["avg_ms"] > self.alert_thresholds["response_time_ms"]:
                alerts.append({
                    "type": "performance",
                    "severity": "warning",
                    "message": f"High response time: {timer_data['avg_ms']:.2f}ms for {timer_name}",
                    "metric": timer_name,
                    "value": timer_data["avg_ms"],
                    "threshold": self.alert_thresholds["response_time_ms"]
                })

        # Check system resource alerts
        for gauge_name, gauge_data in summary.get("gauges", {}).items():
            if "usage_percent" in gauge_name:
                threshold_key = gauge_name.split(".")[-1]  # Extract the last part
                if threshold_key in self.alert_thresholds:
                    if gauge_data["current_value"] > self.alert_thresholds[threshold_key]:
                        alerts.append({
                            "type": "resource",
                            "severity": "critical" if gauge_data["current_value"] > 95 else "warning",
                            "message": f"High {gauge_name}: {gauge_data['current_value']:.2f}%",
                            "metric": gauge_name,
                            "value": gauge_data["current_value"],
                            "threshold": self.alert_thresholds[threshold_key]
                        })

        # Send alerts (with cooldown)
        for alert in alerts:
            await self._send_alert_if_needed(alert)

        return alerts

    async def _send_alert_if_needed(self, alert: Dict[str, Any]):
        """Send alert if cooldown period has passed"""
        alert_key = f"{alert['type']}_{alert['metric']}"
        last_sent = self.alerts_sent.get(alert_key)

        if not last_sent or (datetime.utcnow() - last_sent).total_seconds() > (self.alert_cooldown_minutes * 60):
            await self._send_alert(alert)
            self.alerts_sent[alert_key] = datetime.utcnow()

    async def _send_alert(self, alert: Dict[str, Any]):
        """Send alert notification (implement with your preferred alerting system)"""
        # This would integrate with Slack, PagerDuty, email, etc.
        logging.warning(f"ALERT: {alert['message']} (Value: {alert['value']}, Threshold: {alert['threshold']})")

        # Example: Send to monitoring dashboard or external service
        # await send_to_slack(alert)
        # await send_to_pagerduty(alert)

# Global metrics instance
metrics_collector = MetricsCollector()
performance_monitor = PerformanceMonitor(metrics_collector)
```
### **Milestone 3.4: Containerization & Cloud Readiness**

**Objective**: Prepare application for cloud deployment with Docker and Kubernetes

**Docker Configuration**:
```dockerfile
# Dockerfile
FROM python:3.12-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc g++ curl postgresql-client \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt requirements-prod.txt ./
RUN pip install --no-cache-dir -r requirements-prod.txt

COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

EXPOSE 8000
CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

**Kubernetes Deployment**:
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web-crawler-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: web-crawler-api
  template:
    metadata:
      labels:
        app: web-crawler-api
    spec:
      containers:
      - name: api
        image: web-crawler:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: web-crawler-secrets
              key: database-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
```

**Success Criteria for Phase 3**:
- [ ] Vector search system with sub-second response times
- [ ] REST API handling 1,000+ requests/minute
- [ ] Comprehensive OpenAPI documentation
- [ ] Container deployment with health checks
- [ ] Monitoring and alerting covering all critical metrics
- [ ] 99.9% API uptime with automated failover

---

## 🧠 **PHASE 4: ADVANCED INTELLIGENCE & KNOWLEDGE GRAPHS**
**Priority**: High |

### **Milestone 4.1: Knowledge Graph Construction**

**Objective**: Build comprehensive knowledge graphs for advanced relationship analysis

**Neo4j Graph Database Schema**:
```cypher
// Create node types and relationships
CREATE CONSTRAINT site_domain IF NOT EXISTS FOR (s:Site) REQUIRE s.domain IS UNIQUE;
CREATE CONSTRAINT page_url IF NOT EXISTS FOR (p:Page) REQUIRE p.url IS UNIQUE;
CREATE CONSTRAINT entity_canonical IF NOT EXISTS FOR (e:Entity) REQUIRE e.canonical_name IS UNIQUE;

// Site nodes
CREATE (s:Site {
    domain: $domain,
    title: $title,
    authority_score: $authority_score,
    category: $category,
    language: $language,
    created_at: datetime(),
    last_crawled: datetime($last_crawled)
});

// Page nodes
CREATE (p:Page {
    url: $url,
    title: $title,
    content_hash: $content_hash,
    word_count: $word_count,
    language: $language,
    content_type: $content_type,
    authority_score: $authority_score,
    created_at: datetime(),
    last_crawled: datetime($last_crawled)
});

// Entity nodes
CREATE (e:Entity {
    canonical_name: $canonical_name,
    entity_type: $entity_type,
    confidence_score: $confidence_score,
    frequency_count: $frequency_count,
    wikipedia_id: $wikipedia_id,
    wikidata_id: $wikidata_id
});

// Topic nodes
CREATE (t:Topic {
    name: $topic_name,
    category: $category,
    confidence_score: $confidence_score,
    trending_score: $trending_score
});

// Relationship types
CREATE (s:Site)-[:HOSTS]->(p:Page);
CREATE (p1:Page)-[:LINKS_TO {anchor_text: $anchor_text, link_type: $link_type}]->(p2:Page);
CREATE (p:Page)-[:MENTIONS {frequency: $frequency, context: $context}]->(e:Entity);
CREATE (p:Page)-[:DISCUSSES {relevance_score: $relevance_score}]->(t:Topic);
CREATE (e1:Entity)-[:RELATED_TO {relationship_type: $rel_type, strength: $strength}]->(e2:Entity);
CREATE (t1:Topic)-[:SUBTOPIC_OF]->(t2:Topic);
CREATE (s1:Site)-[:SIMILAR_TO {similarity_score: $similarity}]->(s2:Site);
```

**Knowledge Graph Service**:
```python
# services/knowledge/graph_builder.py
from neo4j import GraphDatabase
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime
import networkx as nx

class KnowledgeGraphBuilder:
    """Build and maintain knowledge graphs from crawled data"""

    def __init__(self, neo4j_uri: str, neo4j_user: str, neo4j_password: str):
        self.driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        self.logger = logging.getLogger(__name__)

        # NetworkX graph for in-memory analysis
        self.analysis_graph = nx.DiGraph()

    async def build_site_relationships(self, site_data: Dict) -> Dict:
        """Build relationships between sites based on linking patterns"""
        try:
            with self.driver.session() as session:
                # Create or update site node
                site_query = """
                MERGE (s:Site {domain: $domain})
                SET s.title = $title,
                    s.authority_score = $authority_score,
                    s.category = $category,
                    s.language = $language,
                    s.last_updated = datetime()
                RETURN s.domain as domain
                """

                result = session.run(site_query, {
                    'domain': site_data['domain'],
                    'title': site_data.get('title'),
                    'authority_score': site_data.get('authority_score', 0.0),
                    'category': site_data.get('category'),
                    'language': site_data.get('language', 'en')
                })

                # Build outbound link relationships
                if 'outbound_links' in site_data:
                    await self._create_site_link_relationships(
                        site_data['domain'],
                        site_data['outbound_links']
                    )

                return {'success': True, 'domain': site_data['domain']}

        except Exception as e:
            self.logger.error(f"Failed to build site relationships: {e}")
            return {'success': False, 'error': str(e)}

    async def build_entity_relationships(self, page_data: Dict) -> Dict:
        """Build entity co-occurrence and relationship networks"""
        try:
            with self.driver.session() as session:
                page_url = page_data['url']
                entities = page_data.get('entities', [])

                # Create page node
                page_query = """
                MERGE (p:Page {url: $url})
                SET p.title = $title,
                    p.content_hash = $content_hash,
                    p.word_count = $word_count,
                    p.language = $language,
                    p.last_updated = datetime()
                RETURN p.url as url
                """

                session.run(page_query, {
                    'url': page_url,
                    'title': page_data.get('title'),
                    'content_hash': page_data.get('content_hash'),
                    'word_count': page_data.get('word_count', 0),
                    'language': page_data.get('language', 'en')
                })

                # Create entity nodes and relationships
                for entity in entities:
                    await self._create_entity_relationships(page_url, entity)

                # Build entity co-occurrence relationships
                await self._build_entity_cooccurrence(page_url, entities)

                return {'success': True, 'entities_processed': len(entities)}

        except Exception as e:
            self.logger.error(f"Failed to build entity relationships: {e}")
            return {'success': False, 'error': str(e)}

    async def _create_entity_relationships(self, page_url: str, entity: Dict):
        """Create entity nodes and page-entity relationships"""
        with self.driver.session() as session:
            entity_query = """
            MERGE (e:Entity {canonical_name: $canonical_name})
            SET e.entity_type = $entity_type,
                e.confidence_score = $confidence_score,
                e.frequency_count = coalesce(e.frequency_count, 0) + $frequency,
                e.wikipedia_id = $wikipedia_id,
                e.wikidata_id = $wikidata_id,
                e.last_updated = datetime()

            WITH e
            MATCH (p:Page {url: $page_url})
            MERGE (p)-[r:MENTIONS]->(e)
            SET r.frequency = $frequency,
                r.context = $context,
                r.confidence = $confidence_score
            """

            session.run(entity_query, {
                'canonical_name': entity['canonical_name'],
                'entity_type': entity['entity_type'],
                'confidence_score': entity.get('confidence_score', 0.0),
                'frequency': entity.get('frequency', 1),
                'wikipedia_id': entity.get('wikipedia_id'),
                'wikidata_id': entity.get('wikidata_id'),
                'page_url': page_url,
                'context': entity.get('context', '')
            })

    async def _build_entity_cooccurrence(self, page_url: str, entities: List[Dict]):
        """Build co-occurrence relationships between entities on the same page"""
        if len(entities) < 2:
            return

        with self.driver.session() as session:
            # Create co-occurrence relationships for all entity pairs
            for i, entity1 in enumerate(entities):
                for entity2 in entities[i+1:]:
                    cooccurrence_query = """
                    MATCH (e1:Entity {canonical_name: $entity1_name})
                    MATCH (e2:Entity {canonical_name: $entity2_name})
                    MERGE (e1)-[r:CO_OCCURS_WITH]-(e2)
                    SET r.frequency = coalesce(r.frequency, 0) + 1,
                        r.last_seen = datetime(),
                        r.strength = coalesce(r.strength, 0.0) + $strength
                    """

                    # Calculate co-occurrence strength based on entity types and confidence
                    strength = min(
                        entity1.get('confidence_score', 0.5),
                        entity2.get('confidence_score', 0.5)
                    )

                    session.run(cooccurrence_query, {
                        'entity1_name': entity1['canonical_name'],
                        'entity2_name': entity2['canonical_name'],
                        'strength': strength
                    })

    async def analyze_topic_clusters(self, min_cluster_size: int = 5) -> List[Dict]:
        """Analyze topic clusters using community detection algorithms"""
        try:
            with self.driver.session() as session:
                # Get topic relationships
                topic_query = """
                MATCH (t1:Topic)-[r:RELATED_TO]-(t2:Topic)
                RETURN t1.name as topic1, t2.name as topic2, r.strength as strength
                """

                results = session.run(topic_query)

                # Build NetworkX graph for analysis
                G = nx.Graph()
                for record in results:
                    G.add_edge(
                        record['topic1'],
                        record['topic2'],
                        weight=record['strength']
                    )

                # Detect communities using Louvain algorithm
                import community as community_louvain
                partition = community_louvain.best_partition(G)

                # Group topics by community
                communities = {}
                for topic, community_id in partition.items():
                    if community_id not in communities:
                        communities[community_id] = []
                    communities[community_id].append(topic)

                # Filter by minimum cluster size and format results
                clusters = []
                for community_id, topics in communities.items():
                    if len(topics) >= min_cluster_size:
                        # Calculate cluster metrics
                        subgraph = G.subgraph(topics)
                        density = nx.density(subgraph)

                        clusters.append({
                            'cluster_id': community_id,
                            'topics': topics,
                            'size': len(topics),
                            'density': density,
                            'representative_topic': max(topics, key=lambda t: G.degree(t))
                        })

                return sorted(clusters, key=lambda x: x['size'], reverse=True)

        except Exception as e:
            self.logger.error(f"Topic cluster analysis failed: {e}")
            return []

    async def get_entity_influence_scores(self, limit: int = 100) -> List[Dict]:
        """Calculate entity influence scores using PageRank algorithm"""
        try:
            with self.driver.session() as session:
                # Get entity relationships
                entity_query = """
                MATCH (e1:Entity)-[r:RELATED_TO|CO_OCCURS_WITH]-(e2:Entity)
                RETURN e1.canonical_name as entity1, e2.canonical_name as entity2,
                       coalesce(r.strength, 1.0) as weight
                """

                results = session.run(entity_query)

                # Build NetworkX graph
                G = nx.Graph()
                for record in results:
                    G.add_edge(
                        record['entity1'],
                        record['entity2'],
                        weight=record['weight']
                    )

                # Calculate PageRank scores
                pagerank_scores = nx.pagerank(G, weight='weight')

                # Get additional entity metadata
                entity_metadata = {}
                metadata_query = """
                MATCH (e:Entity)
                RETURN e.canonical_name as name, e.entity_type as type,
                       e.frequency_count as frequency, e.confidence_score as confidence
                """

                metadata_results = session.run(metadata_query)
                for record in metadata_results:
                    entity_metadata[record['name']] = {
                        'entity_type': record['type'],
                        'frequency': record['frequency'],
                        'confidence': record['confidence']
                    }

                # Combine scores with metadata
                influence_scores = []
                for entity, score in pagerank_scores.items():
                    metadata = entity_metadata.get(entity, {})
                    influence_scores.append({
                        'entity': entity,
                        'influence_score': score,
                        'entity_type': metadata.get('entity_type'),
                        'frequency': metadata.get('frequency', 0),
                        'confidence': metadata.get('confidence', 0.0)
                    })

                return sorted(influence_scores, key=lambda x: x['influence_score'], reverse=True)[:limit]

        except Exception as e:
            self.logger.error(f"Entity influence calculation failed: {e}")
            return []

    def close(self):
        """Close Neo4j driver connection"""
        self.driver.close()
```

### **Milestone 4.2: Advanced Analytics & Trend Detection**

**Objective**: Implement predictive analytics and trend detection systems

**Trend Detection System**:
```python
# services/intelligence/trend_analyzer.py
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
import pandas as pd

class TrendAnalyzer:
    """Detect emerging trends and predict content patterns"""

    def __init__(self, session):
        self.session = session
        self.logger = logging.getLogger(__name__)

    async def detect_emerging_topics(self, timeframe_days: int = 7) -> List[Dict]:
        """Detect rapidly growing topics using time-series analysis"""
        try:
            # Get topic frequency over time
            from sqlalchemy import text

            query = text("""
                SELECT
                    pt.topic_name,
                    DATE_TRUNC('day', p.last_crawled) as date,
                    COUNT(*) as frequency,
                    AVG(pt.relevance_score) as avg_relevance
                FROM page_topics pt
                JOIN pages p ON pt.page_id = p.id
                WHERE p.last_crawled >= NOW() - INTERVAL '%s days'
                GROUP BY pt.topic_name, DATE_TRUNC('day', p.last_crawled)
                ORDER BY pt.topic_name, date
            """ % timeframe_days)

            results = self.session.execute(query).fetchall()

            # Convert to DataFrame for analysis
            df = pd.DataFrame(results, columns=['topic', 'date', 'frequency', 'avg_relevance'])

            trending_topics = []
            for topic in df['topic'].unique():
                topic_data = df[df['topic'] == topic].sort_values('date')

                if len(topic_data) >= 3:  # Need at least 3 data points
                    # Calculate growth rate
                    frequencies = topic_data['frequency'].values
                    growth_rate = self._calculate_growth_rate(frequencies)

                    # Calculate momentum (acceleration)
                    momentum = self._calculate_momentum(frequencies)

                    # Calculate trend score
                    trend_score = (growth_rate * 0.6) + (momentum * 0.4)

                    if trend_score > 0.5:  # Threshold for trending
                        trending_topics.append({
                            'topic': topic,
                            'growth_rate': growth_rate,
                            'momentum': momentum,
                            'trend_score': trend_score,
                            'current_frequency': frequencies[-1],
                            'avg_relevance': float(topic_data['avg_relevance'].mean()),
                            'days_trending': len(topic_data)
                        })

            return sorted(trending_topics, key=lambda x: x['trend_score'], reverse=True)

        except Exception as e:
            self.logger.error(f"Trend detection failed: {e}")
            return []

    def _calculate_growth_rate(self, frequencies: np.ndarray) -> float:
        """Calculate exponential growth rate"""
        if len(frequencies) < 2:
            return 0.0

        # Use log transformation for exponential growth
        log_freq = np.log(frequencies + 1)  # +1 to avoid log(0)

        # Linear regression on log values
        x = np.arange(len(log_freq))
        slope = np.polyfit(x, log_freq, 1)[0]

        return float(slope)

    def _calculate_momentum(self, frequencies: np.ndarray) -> float:
        """Calculate acceleration/momentum of growth"""
        if len(frequencies) < 3:
            return 0.0

        # Calculate second derivative (acceleration)
        first_diff = np.diff(frequencies)
        second_diff = np.diff(first_diff)

        return float(np.mean(second_diff))

**Success Criteria for Phase 4**:
- [ ] Knowledge graph with 1M+ entities and 10M+ relationships
- [ ] Real-time trend detection with 95% accuracy
- [ ] Entity influence scoring operational
- [ ] Topic clustering with coherent communities
- [ ] Predictive analytics for content trends
- [ ] Advanced relationship analysis capabilities

---

## 🚀 **PHASES 5-8: ENTERPRISE SCALE & PRODUCTION DEPLOYMENT**

### **Phase 5: Scale & Performance Optimization** (8 weeks)
- **Distributed Architecture**: Multi-region deployment with data replication
- **Performance Optimization**: Query optimization, caching layers, CDN integration
- **Auto-scaling**: Kubernetes HPA, database sharding, load balancing
- **Monitoring**: Advanced APM, distributed tracing, performance analytics

### **Phase 6: Production Deployment & Operations** (6 weeks)
- **CI/CD Pipeline**: Automated testing, deployment, rollback capabilities
- **Security Hardening**: Penetration testing, security audits, compliance validation
- **Disaster Recovery**: Backup strategies, failover procedures, data recovery
- **Documentation**: Operational runbooks, API documentation, user guides

### **Phase 7: Advanced Intelligence Features** (10 weeks)
- **Predictive Analytics**: Content trend prediction, authority scoring evolution
- **Quality Scoring**: Advanced content quality algorithms, spam detection
- **Personalization**: User-specific search results, recommendation engines
- **Business Intelligence**: Analytics dashboards, reporting systems

### **Phase 8: Enterprise Integration & Ecosystem** (8 weeks)
- **Multi-tenant Architecture**: Enterprise customer isolation and customization
- **API Ecosystem**: Partner integrations, webhook systems, data syndication
- **Business Intelligence**: Advanced analytics, custom reporting, data exports
- **Marketplace Integration**: Third-party tool integrations, plugin architecture

---

## 📊 **IMPLEMENTATION SUMMARY**

### **Technical Stack Evolution**
```
Phase 0-1: Foundation
├── Python 3.12 + PostgreSQL 15
├── SQLAlchemy ORM + Alembic migrations
├── Basic crawling + URL frontier
└── Rate limiting + robots.txt compliance

Phase 2-3: Intelligence & API
├── spaCy + Transformers NLP pipeline
├── pgvector + ChromaDB for semantic search
├── FastAPI + OpenAPI documentation
└── Docker + Kubernetes deployment

Phase 4-8: Enterprise Scale
├── Neo4j knowledge graphs
├── Advanced analytics + ML pipelines
├── Multi-region deployment
└── Enterprise integrations
```

### **Database Architecture**
- **PostgreSQL**: Primary data store (sites, pages, content, metadata)
- **Neo4j**: Knowledge graphs and relationship analysis
- **Redis**: Caching, session management, task queues
- **Vector Databases**: Semantic search and similarity matching

### **Key Performance Targets**
- **Crawling**: 10,000+ pages/hour with respectful rate limiting
- **Search**: Sub-second response times for 95% of queries
- **API**: 10,000+ requests/minute with 99.9% uptime
- **Data Quality**: 95%+ accuracy in content classification and entity extraction
- **Scalability**: Support for 100M+ pages and 1B+ relationships

### **Success Metrics by Phase**
1. **Phase 0-1**: 100K pages crawled, basic infrastructure operational
2. **Phase 2**: 1M pages analyzed, NLP pipeline processing 500+ pages/hour
3. **Phase 3**: API serving 1K+ requests/minute, vector search operational
4. **Phase 4**: Knowledge graph with 1M+ entities, trend detection active
5. **Phase 5-8**: Enterprise-scale deployment serving multiple customers

---

## 🎯 **IMMEDIATE NEXT STEPS**

### **Environment Setup**
1. Activate conda environment: `conda activate web_crawler`
2. Install Phase 0 dependencies from enhanced requirements.txt
3. Set up PostgreSQL database with initial schema
4. Configure development environment and testing framework

### **Foundation Migration**
1. Migrate existing url_discovery.py and content_scraper.py to new architecture
2. Implement URL frontier management system
3. Add robots.txt compliance and rate limiting
4. Set up basic monitoring and logging

### **Core Infrastructure**
1. Implement enhanced content scraping with metadata extraction
2. Build NLP analysis pipeline with entity recognition
3. Set up vector database and semantic search capabilities
4. Create initial API endpoints with authentication

This roadmap transforms the current 2-file prototype into a comprehensive, enterprise-grade Universal Web Directory system, with each phase building systematically toward the full vision while maintaining operational capabilities throughout the development process.
