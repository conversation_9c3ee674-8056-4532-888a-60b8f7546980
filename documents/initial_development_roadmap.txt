--------------- Initial Development Roadmap ---------------

---

### Phase 1: Foundation – Robust, Scalable Crawler

**1. URL Frontier & Queue System**
- **Goal:** Replace file-based URL storage with a scalable, persistent queue supporting distributed crawling and error handling.
- **Action:** Use PostgreSQL for the URL queue, with deduplication, error handling, and support for multiple crawler workers. Add fields for crawl attempts, error messages, and timestamps for distributed coordination.
- **Example Table:**
  ```sql
  CREATE TABLE url_frontier (
      id SERIAL PRIMARY KEY,
      url TEXT UNIQUE NOT NULL,
      status VARCHAR(20) DEFAULT 'pending', -- pending, crawling, crawled, error
      discovered_at TIMESTAMP DEFAULT NOW(),
      last_crawled TIMESTAMP,
      crawl_attempts INT DEFAULT 0,
      error_message TEXT
  );
  CREATE INDEX idx_url_frontier_status ON url_frontier(status);
  ```
- **Python Example (SQLAlchemy):**
  ```python
  from sqlalchemy import create_engine, Column, String, Integer, DateTime, Text
  from sqlalchemy.ext.declarative import declarative_base
  from sqlalchemy.orm import sessionmaker
  import datetime

  Base = declarative_base()
  class URLFrontier(Base):
      __tablename__ = 'url_frontier'
      id = Column(Integer, primary_key=True)
      url = Column(String, unique=True, nullable=False)
      status = Column(String, default='pending')
      discovered_at = Column(DateTime, default=datetime.datetime.utcnow)
      last_crawled = Column(DateTime)
      crawl_attempts = Column(Integer, default=0)
      error_message = Column(Text)
  ```
- **Distributed Crawler Example:**
  - Use `SELECT ... FOR UPDATE SKIP LOCKED` to safely assign URLs to multiple crawler workers:
    ```sql
    BEGIN;
    SELECT id, url FROM url_frontier WHERE status = 'pending' LIMIT 10 FOR UPDATE SKIP LOCKED;
    -- mark as 'crawling' in the same transaction
    COMMIT;
    ```

**2. Robots.txt Compliance**
- **Goal:** Respect site crawling rules and avoid legal/ethical issues.
- **Action:** Use `urllib.robotparser` or `reppy` for advanced robots.txt parsing. Cache robots.txt per domain in the database.
- **Example Table:**
  ```sql
  CREATE TABLE robots_cache (
      domain TEXT PRIMARY KEY,
      robots_txt TEXT,
      fetched_at TIMESTAMP
  );
  ```
- **Python Example:**
  ```python
  import urllib.robotparser
  from urllib.parse import urlparse
  def can_crawl(url):
      domain = urlparse(url).netloc
      # fetch/cached robots.txt logic here
      rp = urllib.robotparser.RobotFileParser()
      rp.set_url(f"http://{domain}/robots.txt")
      rp.read()
      return rp.can_fetch('*', url)
  ```

**3. Rate Limiting**
- **Goal:** Avoid overloading sites and getting IP-banned.
- **Action:** Implement per-domain rate limiting using a database table or Redis. Track last request time per domain.
- **Example Table:**
  ```sql
  CREATE TABLE domain_rate_limit (
      domain TEXT PRIMARY KEY,
      last_request TIMESTAMP,
      min_interval_seconds INT DEFAULT 10
  );
  ```
- **Python Example:**
  ```python
  import time
  def polite_delay(domain, min_interval=10):
      # fetch last_request from DB, sleep if needed, then update
      pass
  ```

**4. Database Integration**
- **Goal:** Store all crawl data in PostgreSQL, with normalization and referential integrity.
- **Tables:** `sites`, `pages`, `links`, `crawl_errors`
- **Example:**
  ```sql
  CREATE TABLE sites (
      id SERIAL PRIMARY KEY,
      domain TEXT UNIQUE,
      robots_txt TEXT,
      last_crawled TIMESTAMP
  );
  CREATE TABLE pages (
      id SERIAL PRIMARY KEY,
      site_id INT REFERENCES sites(id),
      url TEXT UNIQUE,
      html TEXT,
      crawled_at TIMESTAMP,
      http_status INT,
      content_hash TEXT
  );
  CREATE TABLE links (
      id SERIAL PRIMARY KEY,
      from_page INT REFERENCES pages(id),
      to_url TEXT,
      anchor_text TEXT
  );
  CREATE TABLE crawl_errors (
      id SERIAL PRIMARY KEY,
      url TEXT,
      error_message TEXT,
      occurred_at TIMESTAMP DEFAULT NOW()
  );
  ```
- **Python Example:**
  ```python
  # Store a crawled page
  page = Page(site_id=site_id, url=page_url, html=html_content, crawled_at=datetime.datetime.utcnow(), http_status=200)
  session.add(page)
  session.commit()
  # Store discovered links
  for link, anchor in discovered_links:
      session.add(Link(from_page=page.id, to_url=link, anchor_text=anchor))
  session.commit()
  ```

---

### Phase 2: Intelligence Layer – Content Analysis & Metadata

**1. Content Analysis Pipeline**
- **Goal:** Extract structured data from crawled HTML and store in normalized tables.
- **Action:** Use spaCy, transformers, and custom rules for NLP. Store raw HTML, extracted text, and metadata. Add language detection and text extraction steps.
- **Example Table:**
  ```sql
  CREATE TABLE page_text (
      page_id INT PRIMARY KEY REFERENCES pages(id),
      text TEXT,
      language TEXT
  );
  ```
- **Python Example:**
  ```python
  import spacy
  nlp = spacy.load("en_core_web_sm")
  doc = nlp(html_text)
  entities = [(ent.text, ent.label_) for ent in doc.ents]
  # Store in DB
  ```

**2. Topic Classification**
- **Goal:** Assign taxonomy categories using ML models.
- **Action:** Use zero-shot classification (transformers) or fine-tuned models. Store topics in a hierarchical table.
- **Example Table:**
  ```sql
  CREATE TABLE topics (
      id SERIAL PRIMARY KEY,
      name TEXT,
      parent_id INT REFERENCES topics(id)
  );
  CREATE TABLE page_topics (
      page_id INT REFERENCES pages(id),
      topic_id INT REFERENCES topics(id),
      confidence FLOAT
  );
  ```
- **Python Example:**
  ```python
  from transformers import pipeline
  classifier = pipeline("zero-shot-classification")
  result = classifier(text, candidate_labels=[...])
  # Store topic and confidence
  ```

**3. Entity Recognition & Summarization**
- **Goal:** Extract people, companies, locations, and generate summaries.
- **Action:** Use spaCy/transformers for NER and summarization. Store in `entities`, `page_metadata` tables.
- **Example Table:**
  ```sql
  CREATE TABLE entities (
      id SERIAL PRIMARY KEY,
      name TEXT,
      type TEXT
  );
  CREATE TABLE page_entities (
      page_id INT REFERENCES pages(id),
      entity_id INT REFERENCES entities(id),
      context TEXT
  );
  CREATE TABLE page_metadata (
      page_id INT PRIMARY KEY REFERENCES pages(id),
      summary TEXT,
      reading_level TEXT,
      sentiment TEXT
  );
  ```
- **Python Example:**
  ```python
  # Summarization
  from transformers import pipeline
  summarizer = pipeline("summarization")
  summary = summarizer(text)[0]['summary_text']
  # Store summary in DB
  ```

---

### Phase 3: Advanced Search & Architecture

**1. Vector Database Integration**
- **Goal:** Enable semantic search and similarity queries.
- **Action:** Use ChromaDB, FAISS, or pgvector for vector storage. Generate embeddings with `sentence-transformers`.
- **Example Table:**
  ```sql
  CREATE TABLE page_vectors (
      page_id INT PRIMARY KEY REFERENCES pages(id),
      embedding VECTOR(768)
  );
  ```
- **Python Example:**
  ```python
  from sentence_transformers import SentenceTransformer
  model = SentenceTransformer('all-MiniLM-L6-v2')
  embedding = model.encode(text)
  # Store embedding in DB
  ```

**2. API-First Design**
- **Goal:** Expose data via FastAPI for both structured and semantic queries.
- **Action:** Build endpoints for:
  - Structured queries (filter by topic, entity, etc.)
  - Semantic search (vector similarity)
  - Historical and trend queries
- **Python Example:**
  ```python
  from fastapi import FastAPI
  app = FastAPI()
  @app.get("/search")
  def search(q: str):
      # query DB and return results
      pass
  ```

**3. Microservices & Task Queue**
- **Goal:** Decouple components for scalability and reliability.
- **Action:** Use Celery or RQ for distributed task management. Separate services for crawling, NLP, vectorization, and API.
- **Example:**
  - `celery -A tasks worker --loglevel=info`
  - Each worker pulls jobs from a shared queue (e.g., Redis, RabbitMQ)

---

### Phase 4: Full Vision – Universal Web Directory Features

**1. Knowledge Graph Construction**
- **Goal:** Build a graph of sites, pages, entities, and relationships for advanced analysis.
- **Action:** Use `networkx` for in-memory graphs or Neo4j for persistent storage. Model citation networks, topic clusters, and influence graphs.
- **Example:**
  ```python
  import networkx as nx
  G = nx.DiGraph()
  G.add_edge('siteA', 'siteB', type='link')
  # Analyze clusters, centrality, etc.
  ```

**2. Technical & Editorial Profiling**
- **Goal:** Analyze CMS, analytics, publishing patterns, and content quality.
- **Action:**
  - Parse HTML for meta tags, scripts, and technology signatures (e.g., Wappalyzer API)
  - Track content update frequency, editorial calendar, and content types
  - Store technical and editorial metadata in dedicated tables
- **Example Table:**
  ```sql
  CREATE TABLE site_tech_profile (
      site_id INT PRIMARY KEY REFERENCES sites(id),
      cms TEXT,
      analytics TEXT,
      frameworks TEXT,
      mobile_ready BOOLEAN,
      accessibility_score FLOAT
  );
  CREATE TABLE page_content_profile (
      page_id INT PRIMARY KEY REFERENCES pages(id),
      content_type TEXT,
      update_frequency INT,
      editorial_score FLOAT
  );
  ```

**3. Quality & Trend Intelligence**
- **Goal:** Score content for originality, authority, and detect trends and emerging topics.
- **Action:**
  - Implement scoring algorithms for content depth, originality, and authority
  - Use time-series analysis to detect trending topics and site evolution
  - Store trend and quality metrics in the database
- **Python Example:**
  ```python
  # Example: Calculate content originality score
  def originality_score(text):
      # Compare against known content hashes, n-gram overlap, etc.
      pass
  ```

**4. Real-Time & Historical Tracking**
- **Goal:** Monitor changes, broken links, and site evolution over time.
- **Action:**
  - Schedule periodic recrawls and diff content hashes
  - Track broken links and update status in the DB
  - Store historical snapshots for trend analysis
- **Example Table:**
  ```sql
  CREATE TABLE page_history (
      id SERIAL PRIMARY KEY,
      page_id INT REFERENCES pages(id),
      html TEXT,
      crawled_at TIMESTAMP
  );
  CREATE TABLE broken_links (
      id SERIAL PRIMARY KEY,
      from_page INT REFERENCES pages(id),
      to_url TEXT,
      detected_at TIMESTAMP
  );
  ```

---

### Example: Populating PostgreSQL with Discovered Data

**Crawling and Storing Links:**
```python
# After extracting links from a page
for link, anchor in discovered_links:
    session.add(URLFrontier(url=link))
session.commit()
```

**Storing Page Content:**
```python
page = Page(site_id=site_id, url=page_url, html=html_content, crawled_at=datetime.datetime.utcnow(), http_status=200)
session.add(page)
session.commit()
```

**Storing Embeddings:**
```python
embedding = model.encode(page_text)
page_vector = PageVector(page_id=page.id, embedding=embedding)
session.add(page_vector)
session.commit()
```

**Storing NLP Metadata:**
```python
summary = summarizer(page_text)[0]['summary_text']
page_metadata = PageMetadata(page_id=page.id, summary=summary, ...)
session.add(page_metadata)
session.commit()
```

---

## Next Steps

1. **Implement the URL queue in PostgreSQL and migrate your crawler to use it.**
2. **Integrate robots.txt and rate limiting, with per-domain tracking and caching.**
3. **Switch all data storage to PostgreSQL using SQLAlchemy, with normalized schema.**
4. **Build the content analysis pipeline with spaCy/transformers, and store structured metadata.**
5. **Design and implement the API and vector search layer (FastAPI + pgvector/ChromaDB).**
6. **Iteratively add advanced profiling, quality scoring, and trend detection features.**
7. **Develop microservices for crawling, NLP, vectorization, and API, orchestrated by a task queue.**
8. **Begin knowledge graph construction and advanced relationship analysis.**
9. **Implement real-time and historical tracking, including broken link detection and content evolution.**
10. **Continuously refine models and scoring algorithms for quality, authority, and trend intelligence.**

---

## References & Further Reading
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [SQLAlchemy ORM Tutorial](https://docs.sqlalchemy.org/en/20/orm/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [spaCy NLP Library](https://spacy.io/)
- [HuggingFace Transformers](https://huggingface.co/docs/transformers/index)
- [pgvector for PostgreSQL](https://github.com/pgvector/pgvector)
- [ChromaDB Vector Database](https://docs.trychroma.com/)
- [Celery Task Queue](https://docs.celeryq.dev/en/stable/)
- [Neo4j Graph Database](https://neo4j.com/developer/)
