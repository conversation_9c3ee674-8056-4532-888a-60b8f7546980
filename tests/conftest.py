# tests/conftest.py
import pytest
import os
import tempfile
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Add project root to Python path
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from models.base import Base
from config.database import DatabaseConfig
# Imports from roadmap for Milestone 2.3
import asyncio
from services.crawler.content_scraper import EnhancedContentScraper
from services.nlp.content_analyzer import ContentAnalyzer


@pytest.fixture(scope="session")
def test_database():
    """Create test database for integration tests (PostgreSQL as per roadmap)"""
    # Note: This uses PostgreSQL as specified in the roadmap.
    # The previous version used SQLite. If specific PostgreSQL features aren't needed
    # immediately for scraper/analyzer tests, SQLite could be faster for unit tests.
    # However, aligning with the roadmap for future integration tests.
    db_config = DatabaseConfig()
    test_db_url = f"postgresql://{os.getenv('TEST_DB_USER', 'test_user')}:{os.getenv('TEST_DB_PASSWORD', 'test_pass')}@{os.getenv('TEST_DB_HOST', 'localhost')}:{os.getenv('TEST_DB_PORT', '5432')}/{os.getenv('TEST_DB_NAME', 'test_web_crawler')}"
    
    # Fallback for local testing if env vars not set, matching roadmap
    if db_config.username == 'uwd_app': # Default username from DatabaseConfig
        test_db_url = "postgresql://test_user:test_pass@localhost/test_web_crawler"

    engine = create_engine(test_db_url)
    try:
        Base.metadata.create_all(engine)
        yield engine
    finally:
        Base.metadata.drop_all(engine)
        engine.dispose()


@pytest.fixture(scope="function") # Changed to function scope to match roadmap, was "function" already
def db_session(test_database):
    """Create database session for tests"""
    Session = sessionmaker(bind=test_database)
    session = Session()
    yield session
    session.rollback()
    session.close()

@pytest.fixture
def sample_html():
    """Sample HTML content for testing"""
    return """
    <html>
        <head><title>Test Page</title></head>
        <body>
            <h1>Welcome to Test Corp</h1>
            <p>John Smith is the CEO of Test Corp, located in New York.</p>
            <a href="https://example.com">External Link</a>
        </body>
    </html>
    """

@pytest.fixture
def sample_site_data():
    """Sample site data for testing"""
    return {
        'domain': 'example.com',
        'title': 'Example Domain',
        'description': 'This domain is for use in illustrative examples',
        'language': 'en'
    }

@pytest.fixture
def sample_page_data():
    """Sample page data for testing"""
    return {
        'url': 'https://example.com/test-page',
        'title': 'Test Page',
        'html_content': '<html><body><h1>Test Page</h1><p>This is a test page.</p></body></html>',
        'text_content': 'Test Page\nThis is a test page.',
        'http_status': 200,
        'content_type': 'text/html'
    }
