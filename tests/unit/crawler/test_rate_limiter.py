import asyncio
import unittest
from unittest.mock import MagicMock, patch, call
import datetime as dt # Changed for consistency
from datetime import timedelta

# Assuming the models and AdaptiveRateLimiter are structured as previously discussed
# Adjust import paths if necessary
from services.crawler.rate_limiter import AdaptiveRateLimiter
from models.rate_limit_tracking import RateLimitTracking
from models.request_history import RequestHistory

class TestAdaptiveRateLimiter(unittest.TestCase):

    def setUp(self):
        self.mock_session = MagicMock()
        self.worker_id = "test_worker_01"
        self.rate_limiter = AdaptiveRateLimiter(session=self.mock_session, worker_id=self.worker_id)

        # Default settings for the limiter, mirror what's in the class
        self.default_delay_ms = 1000
        self.min_delay_ms = 100
        self.max_delay_ms = 30000
        self.adaptive_factor_decrease = 0.9
        self.adaptive_factor_increase = 1.2

    def _run_async(self, coro):
        return asyncio.run(coro)

    def test_initial_get_rate_limit_state_creates_new(self):
        domain = "example.com"
        self.mock_session.query(RateLimitTracking).filter_by(domain=domain, worker_id=self.worker_id).first.return_value = None

        # Mock add and commit
        self.mock_session.add = MagicMock()
        self.mock_session.commit = MagicMock()

        state = self.rate_limiter._get_rate_limit_state(domain)

        self.mock_session.query(RateLimitTracking).filter_by(domain=domain, worker_id=self.worker_id).first.assert_called_once()
        self.mock_session.add.assert_called_once()
        self.mock_session.commit.assert_called_once()

        self.assertIsNotNone(state)
        self.assertEqual(state.domain, domain)
        self.assertEqual(state.worker_id, self.worker_id)
        self.assertEqual(state.current_delay_ms, self.default_delay_ms)
        self.assertLessEqual(state.next_allowed_request, dt.datetime.now(dt.UTC)) # Should allow first request

    def test_get_rate_limit_state_returns_existing(self):
        domain = "existing.com"
        mock_existing_state = RateLimitTracking(
            domain=domain,
            worker_id=self.worker_id,
            current_delay_ms=500,
            next_allowed_request=dt.datetime.now(dt.UTC) - timedelta(seconds=10) # Already allowed
        )
        self.mock_session.query(RateLimitTracking).filter_by(domain=domain, worker_id=self.worker_id).first.return_value = mock_existing_state

        state = self.rate_limiter._get_rate_limit_state(domain)

        self.mock_session.query(RateLimitTracking).filter_by(domain=domain, worker_id=self.worker_id).first.assert_called_once()
        self.assertEqual(state, mock_existing_state)

    def test_calculate_delay_no_delay_needed(self):
        rate_state = RateLimitTracking(next_allowed_request=dt.datetime.now(dt.UTC) - timedelta(seconds=1))
        delay_ms = self.rate_limiter._calculate_delay(rate_state, dt.datetime.now(dt.UTC))
        self.assertEqual(delay_ms, 0)

    def test_calculate_delay_needed(self):
        future_time = dt.datetime.now(dt.UTC) + timedelta(milliseconds=1500)
        rate_state = RateLimitTracking(next_allowed_request=future_time)
        # Ensure a small buffer for execution time if needed, or mock current_time precisely
        delay_ms = self.rate_limiter._calculate_delay(rate_state, dt.datetime.now(dt.UTC))
        self.assertGreater(delay_ms, 1400) # Check it's close to 1500ms
        self.assertLessEqual(delay_ms, 1500)

    @patch('asyncio.sleep', return_value=None) # Mock asyncio.sleep
    def test_wait_if_needed_calls_sleep_when_delay_is_positive(self, mock_sleep):
        domain = "waitforme.com"
        future_time = dt.datetime.now(dt.UTC) + timedelta(milliseconds=500)
        mock_state = RateLimitTracking(
            domain=domain, worker_id=self.worker_id,
            next_allowed_request=future_time, current_delay_ms=500
        )

        # Patch internal methods
        self.rate_limiter._get_rate_limit_state = MagicMock(return_value=mock_state)
        self.rate_limiter._calculate_delay = MagicMock(return_value=500) # Simulate 500ms delay needed
        self.rate_limiter._update_rate_limit_state_before_request = MagicMock()

        delay_seconds = self._run_async(self.rate_limiter.wait_if_needed(domain))

        self.assertAlmostEqual(delay_seconds, 0.5)
        mock_sleep.assert_called_once_with(0.5)
        self.rate_limiter._get_rate_limit_state.assert_called_once_with(domain)
        self.rate_limiter._calculate_delay.assert_called_once()
        self.rate_limiter._update_rate_limit_state_before_request.assert_called_once_with(domain, mock_state)

    @patch('asyncio.sleep', return_value=None)
    def test_wait_if_needed_no_sleep_when_delay_is_zero(self, mock_sleep):
        domain = "dontwait.com"
        past_time = dt.datetime.now(dt.UTC) - timedelta(milliseconds=500)
        mock_state = RateLimitTracking(
            domain=domain, worker_id=self.worker_id,
            next_allowed_request=past_time, current_delay_ms=100
        )

        self.rate_limiter._get_rate_limit_state = MagicMock(return_value=mock_state)
        self.rate_limiter._calculate_delay = MagicMock(return_value=0) # Simulate 0ms delay needed
        self.rate_limiter._update_rate_limit_state_before_request = MagicMock()

        delay_seconds = self._run_async(self.rate_limiter.wait_if_needed(domain))

        self.assertEqual(delay_seconds, 0.0)
        mock_sleep.assert_not_called()
        self.rate_limiter._update_rate_limit_state_before_request.assert_called_once_with(domain, mock_state)

    def test_adjust_delay_adaptive_success(self):
        initial_delay_ms = 1000
        rate_state = RateLimitTracking(current_delay_ms=initial_delay_ms, consecutive_failures=2)

        self.rate_limiter._adjust_delay_adaptive(rate_state, success=True)

        self.assertEqual(rate_state.consecutive_failures, 0)
        expected_delay = int(initial_delay_ms * self.adaptive_factor_decrease)
        self.assertEqual(rate_state.current_delay_ms, expected_delay)
        self.assertGreaterEqual(rate_state.next_allowed_request, dt.datetime.now(dt.UTC) + timedelta(milliseconds=expected_delay - 100)) # allow small diff

    def test_adjust_delay_adaptive_failure(self):
        initial_delay_ms = 1000
        rate_state = RateLimitTracking(current_delay_ms=initial_delay_ms, consecutive_failures=0)

        self.rate_limiter._adjust_delay_adaptive(rate_state, success=False)

        self.assertEqual(rate_state.consecutive_failures, 1)
        expected_delay = int(initial_delay_ms * self.adaptive_factor_increase)
        self.assertEqual(rate_state.current_delay_ms, expected_delay)

    def test_adjust_delay_adaptive_min_max_bounds(self):
        # Test min bound
        rate_state_min = RateLimitTracking(current_delay_ms=self.min_delay_ms + 50, consecutive_failures=0) # e.g. 150
        self.rate_limiter.adaptive_factor_decrease = 0.1 # Drastic decrease to hit min
        self.rate_limiter._adjust_delay_adaptive(rate_state_min, success=True)
        self.assertEqual(rate_state_min.current_delay_ms, self.min_delay_ms)
        self.rate_limiter.adaptive_factor_decrease = 0.9 # Reset for other tests

        # Test max bound
        rate_state_max = RateLimitTracking(current_delay_ms=self.max_delay_ms - 1000, consecutive_failures=self.rate_limiter.failure_threshold + 5)
        self.rate_limiter.adaptive_factor_increase = 2.0 # Drastic increase
        self.rate_limiter._adjust_delay_adaptive(rate_state_max, success=False)
        self.assertEqual(rate_state_max.current_delay_ms, self.max_delay_ms)
        self.rate_limiter.adaptive_factor_increase = 1.2 # Reset

    def test_record_request_result_adds_to_history_and_updates_rate_state(self):
        domain = "testrecord.com"
        url = f"http://{domain}/path"

        mock_rate_state = RateLimitTracking(
            domain=domain, worker_id=self.worker_id,
            current_delay_ms=self.default_delay_ms,
            success_rate=1.0,
            consecutive_failures=0
        )
        self.rate_limiter._get_rate_limit_state = MagicMock(return_value=mock_rate_state)
        self.rate_limiter._adjust_delay_adaptive = MagicMock() # We test this separately

        self.mock_session.add = MagicMock()
        self.mock_session.commit = MagicMock()

        self.rate_limiter.record_request_result(
            domain=domain, url=url, success=True,
            response_time_ms=100, status_code=200, content_length=1024
        )

        # Check history entry was added
        self.mock_session.add.assert_called_once()
        history_arg = self.mock_session.add.call_args[0][0]
        self.assertIsInstance(history_arg, RequestHistory)
        self.assertEqual(history_arg.domain, domain)
        self.assertEqual(history_arg.url, url)
        self.assertTrue(history_arg.success)

        # Check rate state was adjusted
        self.rate_limiter._adjust_delay_adaptive.assert_called_once_with(mock_rate_state, True)

        # Check success rate update (simplified check based on current logic)
        self.assertAlmostEqual(mock_rate_state.success_rate, (1.0 * 0.9) + (1.0 * 0.1))


        # Check commit was called
        self.mock_session.commit.assert_called_once()

    def test_update_rate_limit_state_before_request(self):
        domain = "updateme.com"
        mock_rate_state = RateLimitTracking(domain=domain, worker_id=self.worker_id)

        self.rate_limiter._update_rate_limit_state_before_request(domain, mock_rate_state)

        self.assertIsNotNone(mock_rate_state.last_request_at)
        # Ensure it's recent
        self.assertGreaterEqual(dt.datetime.now(dt.UTC), mock_rate_state.last_request_at)
        self.assertLessEqual((dt.datetime.now(dt.UTC) - mock_rate_state.last_request_at).total_seconds(), 1)
        self.mock_session.commit.assert_called_once()


if __name__ == '__main__':
    unittest.main()
