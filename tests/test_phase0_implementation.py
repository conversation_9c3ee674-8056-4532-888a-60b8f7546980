#!/usr/bin/env python3
"""
Tests for Phase 0 Implementation
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.url_discovery import URLDiscoveryEngine
from core.content_scraper import ContentScraper
from core.crawl_coordinator import CrawlCoordinator
from models import Site, URLFrontier, Page

class TestURLDiscoveryEngine:
    """Test URL Discovery Engine"""
    
    def test_initialization(self):
        """Test URL discovery engine initialization"""
        engine = URLDiscoveryEngine()
        assert engine.respect_robots == True
        assert engine.crawl_delay == 1
        assert engine.session is not None
    
    def test_add_seed_url(self, test_session):
        """Test adding seed URLs"""
        engine = URLDiscoveryEngine()
        
        # Mock the database session
        with patch('core.url_discovery.get_db_session') as mock_session:
            mock_session.return_value.__next__.return_value = test_session
            
            result = engine.add_seed_url('https://example.com')
            assert result == True
            
            # Check that site was created
            site = test_session.query(Site).filter(Site.domain == 'example.com').first()
            assert site is not None
            assert site.domain == 'example.com'
            
            # Check that URL was added to frontier
            url_entry = test_session.query(URLFrontier).filter(URLFrontier.url == 'https://example.com').first()
            assert url_entry is not None
            assert url_entry.status == 'pending'
    
    def test_discover_urls_from_page(self):
        """Test URL discovery from HTML content"""
        engine = URLDiscoveryEngine()
        
        html_content = """
        <html>
        <body>
            <a href="https://example.com/page1">Page 1</a>
            <a href="/page2">Page 2</a>
            <a href="mailto:<EMAIL>">Email</a>
            <a href="javascript:void(0)">JS Link</a>
        </body>
        </html>
        """
        
        urls = engine.discover_urls_from_page(
            'https://example.com', html_content, 'site-id', 1
        )
        
        # Should find HTTP(S) URLs only
        assert len(urls) >= 2
        assert 'https://example.com/page1' in urls
        assert 'https://example.com/page2' in urls  # Relative URL converted to absolute
        assert 'mailto:<EMAIL>' not in urls  # Email links excluded
        assert 'javascript:void(0)' not in urls  # JS links excluded

class TestContentScraper:
    """Test Content Scraper"""
    
    def test_initialization(self):
        """Test content scraper initialization"""
        scraper = ContentScraper()
        assert scraper.request_delay == 1
        assert scraper.timeout == 30
        assert scraper.session is not None
    
    def test_extract_title(self):
        """Test title extraction"""
        scraper = ContentScraper()
        
        from bs4 import BeautifulSoup
        
        # Test with title tag
        html = "<html><head><title>Test Page</title></head><body></body></html>"
        soup = BeautifulSoup(html, 'html.parser')
        title = scraper._extract_title(soup)
        assert title == "Test Page"
        
        # Test with h1 fallback
        html = "<html><body><h1>Header Title</h1></body></html>"
        soup = BeautifulSoup(html, 'html.parser')
        title = scraper._extract_title(soup)
        assert title == "Header Title"
        
        # Test with no title
        html = "<html><body><p>No title</p></body></html>"
        soup = BeautifulSoup(html, 'html.parser')
        title = scraper._extract_title(soup)
        assert title == "No title found"
    
    def test_extract_meta_description(self):
        """Test meta description extraction"""
        scraper = ContentScraper()
        
        from bs4 import BeautifulSoup
        
        # Test with meta description
        html = '<html><head><meta name="description" content="Test description"></head></html>'
        soup = BeautifulSoup(html, 'html.parser')
        desc = scraper._extract_meta_description(soup)
        assert desc == "Test description"
        
        # Test with no meta description
        html = "<html><head></head></html>"
        soup = BeautifulSoup(html, 'html.parser')
        desc = scraper._extract_meta_description(soup)
        assert desc is None
    
    def test_extract_headings(self):
        """Test heading extraction"""
        scraper = ContentScraper()
        
        from bs4 import BeautifulSoup
        
        html = """
        <html>
        <body>
            <h1>Main Title</h1>
            <h2>Subtitle 1</h2>
            <h2>Subtitle 2</h2>
            <h3>Sub-subtitle</h3>
        </body>
        </html>
        """
        soup = BeautifulSoup(html, 'html.parser')
        headings = scraper._extract_headings(soup)
        
        assert len(headings['h1']) == 1
        assert headings['h1'][0] == "Main Title"
        assert len(headings['h2']) == 2
        assert "Subtitle 1" in headings['h2']
        assert "Subtitle 2" in headings['h2']
        assert len(headings['h3']) == 1
        assert headings['h3'][0] == "Sub-subtitle"
    
    def test_calculate_content_hash(self):
        """Test content hash calculation"""
        scraper = ContentScraper()
        
        content1 = "This is test content"
        content2 = "This is test content"
        content3 = "This is different content"
        
        hash1 = scraper._calculate_content_hash(content1)
        hash2 = scraper._calculate_content_hash(content2)
        hash3 = scraper._calculate_content_hash(content3)
        
        assert hash1 == hash2  # Same content should have same hash
        assert hash1 != hash3  # Different content should have different hash
        assert len(hash1) == 64  # SHA-256 hash should be 64 characters

class TestCrawlCoordinator:
    """Test Crawl Coordinator"""
    
    def test_initialization(self):
        """Test crawl coordinator initialization"""
        coordinator = CrawlCoordinator()
        assert coordinator.max_concurrent == 5
        assert coordinator.crawl_delay == 1
        assert coordinator.max_depth == 3
        assert coordinator.url_discovery is not None
        assert coordinator.content_scraper is not None
        assert coordinator.stats['pages_crawled'] == 0
    
    def test_add_seed_urls(self):
        """Test adding multiple seed URLs"""
        coordinator = CrawlCoordinator()
        
        # Mock the URL discovery engine
        with patch.object(coordinator.url_discovery, 'add_seed_url') as mock_add:
            mock_add.return_value = True
            
            urls = ['https://example.com', 'https://test.com']
            added_count = coordinator.add_seed_urls(urls)
            
            assert added_count == 2
            assert mock_add.call_count == 2
    
    def test_get_crawl_stats(self):
        """Test getting crawl statistics"""
        coordinator = CrawlCoordinator()
        
        # Mock frontier stats
        with patch.object(coordinator.url_discovery, 'get_frontier_stats') as mock_stats:
            mock_stats.return_value = {
                'pending': 10,
                'processing': 2,
                'completed': 5,
                'failed': 1
            }
            
            stats = coordinator.get_crawl_stats()
            
            assert 'pages_crawled' in stats
            assert 'urls_discovered' in stats
            assert 'errors' in stats
            assert 'frontier_stats' in stats
            assert stats['frontier_stats']['pending'] == 10

class TestIntegration:
    """Integration tests"""
    
    @pytest.mark.integration
    def test_full_crawl_workflow(self, test_session):
        """Test complete crawl workflow"""
        # This test requires actual HTTP requests, so we'll mock them
        with patch('requests.Session.get') as mock_get:
            # Mock HTTP response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = """
            <html>
            <head><title>Test Page</title></head>
            <body>
                <h1>Welcome</h1>
                <a href="/page2">Page 2</a>
            </body>
            </html>
            """
            mock_response.content = mock_response.text.encode()
            mock_response.headers = {'content-type': 'text/html'}
            mock_response.elapsed.total_seconds.return_value = 0.5
            mock_get.return_value = mock_response
            
            # Initialize coordinator
            coordinator = CrawlCoordinator()
            
            # Mock database sessions
            with patch('core.url_discovery.get_db_session') as mock_session1, \
                 patch('core.content_scraper.get_db_session') as mock_session2, \
                 patch('core.crawl_coordinator.get_db_session') as mock_session3:
                
                mock_session1.return_value.__next__.return_value = test_session
                mock_session2.return_value.__next__.return_value = test_session
                mock_session3.return_value.__next__.return_value = test_session
                
                # Add seed URL
                coordinator.add_seed_urls(['https://example.com'])
                
                # Verify site was created
                site = test_session.query(Site).filter(Site.domain == 'example.com').first()
                assert site is not None
                
                # Verify URL was added to frontier
                url_entry = test_session.query(URLFrontier).filter(
                    URLFrontier.url == 'https://example.com'
                ).first()
                assert url_entry is not None
                assert url_entry.status == 'pending'

def test_database_models_integration(test_session):
    """Test database models work together correctly"""
    # Create a site
    site = Site(
        domain='example.com',
        title='Example Site',
        description='Test site'
    )
    test_session.add(site)
    test_session.commit()
    test_session.refresh(site)
    
    # Create a URL frontier entry
    url_entry = URLFrontier(
        url='https://example.com/test',
        site_id=site.id,
        priority=5,
        depth=0,
        status='pending'
    )
    test_session.add(url_entry)
    test_session.commit()
    test_session.refresh(url_entry)
    
    # Create a page
    page = Page(
        url='https://example.com/test',
        site_id=site.id,
        title='Test Page',
        content_hash='test-hash',
        html_content='<html><body>Test</body></html>',
        text_content='Test',
        content_type='text/html',
        content_length=100,
        response_time=0.5,
        status_code=200,
        crawl_count=1
    )
    test_session.add(page)
    test_session.commit()
    test_session.refresh(page)
    
    # Verify relationships
    assert site.pages[0].id == page.id
    assert page.site.domain == 'example.com'
    assert url_entry.site.domain == 'example.com'
