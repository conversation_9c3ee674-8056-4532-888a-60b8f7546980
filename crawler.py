#!/usr/bin/env python3
"""
Universal Web Directory Crawler - Phase 0 Implementation
Main entry point for the web crawling system
"""

import sys
import argparse
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.crawl_coordinator import CrawlCoordinator
from core.url_discovery import URLDiscoveryEngine
from core.content_scraper import ContentScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main crawler entry point"""
    parser = argparse.ArgumentParser(description='Universal Web Directory Crawler')
    
    # Subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Add seed URLs command
    seed_parser = subparsers.add_parser('seed', help='Add seed URLs')
    seed_parser.add_argument('urls', nargs='+', help='URLs to add as seeds')
    
    # Crawl command
    crawl_parser = subparsers.add_parser('crawl', help='Start crawling')
    crawl_parser.add_argument('--batch-size', type=int, default=10, help='Batch size for crawling')
    crawl_parser.add_argument('--max-pages', type=int, default=100, help='Maximum pages to crawl')
    crawl_parser.add_argument('--max-time', type=int, default=60, help='Maximum time in minutes')
    crawl_parser.add_argument('--continuous', action='store_true', help='Run continuous crawling')
    crawl_parser.add_argument('--delay', type=float, default=1.0, help='Delay between requests in seconds')
    
    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show crawling statistics')
    
    # Sites command
    sites_parser = subparsers.add_parser('sites', help='Show site summary')
    
    # Reset command
    reset_parser = subparsers.add_parser('reset', help='Reset failed URLs')
    
    # Test command
    test_parser = subparsers.add_parser('test', help='Test single URL')
    test_parser.add_argument('url', help='URL to test')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'seed':
            handle_seed_command(args)
        elif args.command == 'crawl':
            handle_crawl_command(args)
        elif args.command == 'stats':
            handle_stats_command(args)
        elif args.command == 'sites':
            handle_sites_command(args)
        elif args.command == 'reset':
            handle_reset_command(args)
        elif args.command == 'test':
            handle_test_command(args)
        else:
            print(f"Unknown command: {args.command}")
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\nOperation interrupted by user")
    except Exception as e:
        logger.error(f"Error executing command: {e}")
        sys.exit(1)

def handle_seed_command(args):
    """Handle seed URL addition"""
    print(f"Adding {len(args.urls)} seed URLs...")
    
    coordinator = CrawlCoordinator()
    added_count = coordinator.add_seed_urls(args.urls)
    
    print(f"Successfully added {added_count} seed URLs")
    
    # Show current frontier stats
    stats = coordinator.url_discovery.get_frontier_stats()
    print(f"Frontier now has {stats.get('pending', 0)} pending URLs")

def handle_crawl_command(args):
    """Handle crawling command"""
    print("🚀 Starting Universal Web Directory Crawler")
    print(f"Configuration:")
    print(f"  - Batch size: {args.batch_size}")
    print(f"  - Max pages: {args.max_pages}")
    print(f"  - Max time: {args.max_time} minutes")
    print(f"  - Request delay: {args.delay} seconds")
    print(f"  - Continuous: {args.continuous}")
    print()
    
    coordinator = CrawlCoordinator(crawl_delay=args.delay)
    
    if args.continuous:
        # Run continuous crawling
        final_stats = coordinator.run_continuous_crawl(
            max_pages=args.max_pages,
            max_time_minutes=args.max_time
        )
    else:
        # Run single batch
        final_stats = coordinator.run_crawl_batch(batch_size=args.batch_size)
    
    print("\n📊 Final Crawl Statistics:")
    print_stats(final_stats)

def handle_stats_command(args):
    """Handle stats command"""
    coordinator = CrawlCoordinator()
    stats = coordinator.get_crawl_stats()
    
    print("📊 Current Crawling Statistics:")
    print_stats(stats)

def handle_sites_command(args):
    """Handle sites command"""
    coordinator = CrawlCoordinator()
    sites = coordinator.get_site_summary()
    
    print("🌐 Site Summary:")
    print(f"{'Domain':<30} {'Pages':<8} {'Pending':<8} {'Created'}")
    print("-" * 60)
    
    for site in sites:
        created = site['created_at'].strftime('%Y-%m-%d') if site['created_at'] else 'Unknown'
        print(f"{site['domain']:<30} {site['pages_crawled']:<8} {site['pending_urls']:<8} {created}")
    
    if not sites:
        print("No sites found in database")

def handle_reset_command(args):
    """Handle reset failed URLs command"""
    coordinator = CrawlCoordinator()
    reset_count = coordinator.reset_failed_urls()
    
    print(f"Reset {reset_count} failed URLs to pending status")

def handle_test_command(args):
    """Handle test single URL command"""
    print(f"Testing URL: {args.url}")
    
    scraper = ContentScraper()
    page_id = scraper.scrape_and_save(args.url)
    
    if page_id:
        print(f"✅ Successfully scraped URL -> Page ID: {page_id}")
    else:
        print("❌ Failed to scrape URL")

def print_stats(stats):
    """Print formatted statistics"""
    print(f"  Pages crawled: {stats.get('pages_crawled', 0)}")
    print(f"  URLs discovered: {stats.get('urls_discovered', 0)}")
    print(f"  Errors: {stats.get('errors', 0)}")
    
    if stats.get('runtime_seconds'):
        runtime = stats['runtime_seconds']
        hours = int(runtime // 3600)
        minutes = int((runtime % 3600) // 60)
        seconds = int(runtime % 60)
        print(f"  Runtime: {hours:02d}:{minutes:02d}:{seconds:02d}")
    
    if stats.get('frontier_stats'):
        frontier = stats['frontier_stats']
        print(f"  Frontier status:")
        print(f"    - Pending: {frontier.get('pending', 0)}")
        print(f"    - Processing: {frontier.get('processing', 0)}")
        print(f"    - Completed: {frontier.get('completed', 0)}")
        print(f"    - Failed: {frontier.get('failed', 0)}")
        print(f"    - Total sites: {frontier.get('total_sites', 0)}")
        print(f"    - Total pages: {frontier.get('total_pages', 0)}")

if __name__ == "__main__":
    main()
