#!/usr/bin/env python3
"""
Enhanced URL Discovery System - Phase 0 Implementation
Replaces the basic url_discovery.py with database-integrated functionality
"""

import sys
import logging
import time
from urllib.parse import urljoin, urlparse, urlunparse
from urllib.robotparser import <PERSON><PERSON><PERSON>Parser
from typing import List, Set, Optional, Tuple
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.database import initialize_database, get_db_session
from models import Site, URLFrontier, Page, CrawlLog
import requests
from bs4 import BeautifulSoup

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class URLDiscoveryEngine:
    """Enhanced URL discovery with database integration"""
    
    def __init__(self, respect_robots=True, crawl_delay=1):
        self.respect_robots = respect_robots
        self.crawl_delay = crawl_delay
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'UniversalWebDirectory/1.0 (+https://github.com/mjsmorgan/web_crawler)'
        })
        
        # Initialize database
        self.engine, self.SessionLocal = initialize_database()
        logger.info("URL Discovery Engine initialized")
    
    def add_seed_url(self, url: str, priority: int = 5) -> bool:
        """Add a seed URL to start crawling from"""
        try:
            with next(get_db_session()) as db:
                # Parse URL to get domain
                parsed = urlparse(url)
                domain = parsed.netloc
                
                # Create or get site
                site = db.query(Site).filter(Site.domain == domain).first()
                if not site:
                    site = Site(
                        domain=domain,
                        title=f"Site: {domain}",
                        description=f"Automatically discovered site: {domain}"
                    )
                    db.add(site)
                    db.commit()
                    db.refresh(site)
                    logger.info(f"Created new site: {domain}")
                
                # Check if URL already exists in frontier
                existing = db.query(URLFrontier).filter(
                    URLFrontier.url == url,
                    URLFrontier.site_id == site.id
                ).first()
                
                if not existing:
                    # Add to URL frontier
                    url_entry = URLFrontier(
                        url=url,
                        site_id=site.id,
                        priority=priority,
                        depth=0,
                        status='pending'
                    )
                    db.add(url_entry)
                    db.commit()
                    logger.info(f"Added seed URL to frontier: {url}")
                    return True
                else:
                    logger.info(f"URL already in frontier: {url}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error adding seed URL {url}: {e}")
            return False
    
    def get_robots_txt(self, site: Site) -> Optional[str]:
        """Fetch and cache robots.txt for a site"""
        try:
            robots_url = f"https://{site.domain}/robots.txt"
            response = self.session.get(robots_url, timeout=10)
            
            if response.status_code == 200:
                robots_content = response.text
                
                # Update site with robots.txt content
                with next(get_db_session()) as db:
                    db_site = db.query(Site).filter(Site.id == site.id).first()
                    if db_site:
                        db_site.robots_txt = robots_content
                        db_site.robots_last_fetched = db.execute("SELECT NOW()").scalar()
                        db.commit()
                
                logger.info(f"Fetched robots.txt for {site.domain}")
                return robots_content
            else:
                logger.info(f"No robots.txt found for {site.domain}")
                return None
                
        except Exception as e:
            logger.warning(f"Error fetching robots.txt for {site.domain}: {e}")
            return None
    
    def is_crawl_allowed(self, url: str, site: Site) -> bool:
        """Check if crawling is allowed based on robots.txt"""
        if not self.respect_robots:
            return True
        
        try:
            # Get robots.txt if not cached or outdated
            if not site.robots_txt or not site.robots_last_fetched:
                self.get_robots_txt(site)
            
            if site.robots_txt:
                rp = RobotFileParser()
                rp.set_url(f"https://{site.domain}/robots.txt")
                rp.read()
                
                user_agent = self.session.headers.get('User-Agent', '*')
                return rp.can_fetch(user_agent, url)
            
            # If no robots.txt, assume crawling is allowed
            return True
            
        except Exception as e:
            logger.warning(f"Error checking robots.txt for {url}: {e}")
            return True
    
    def discover_urls_from_page(self, page_url: str, html_content: str, site_id: str, max_depth: int = 3) -> List[str]:
        """Extract URLs from a page's HTML content"""
        discovered_urls = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            base_url = page_url
            
            # Find all links
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if not href:
                    continue
                
                # Convert relative URLs to absolute
                absolute_url = urljoin(base_url, href)
                
                # Parse URL
                parsed = urlparse(absolute_url)
                
                # Skip non-HTTP(S) URLs
                if parsed.scheme not in ['http', 'https']:
                    continue
                
                # Clean URL (remove fragment)
                clean_url = urlunparse((
                    parsed.scheme,
                    parsed.netloc,
                    parsed.path,
                    parsed.params,
                    parsed.query,
                    ''  # Remove fragment
                ))
                
                discovered_urls.append(clean_url)
            
            logger.info(f"Discovered {len(discovered_urls)} URLs from {page_url}")
            return discovered_urls
            
        except Exception as e:
            logger.error(f"Error discovering URLs from {page_url}: {e}")
            return []
    
    def add_discovered_urls(self, urls: List[str], source_page_id: str, depth: int) -> int:
        """Add discovered URLs to the frontier"""
        added_count = 0
        
        try:
            with next(get_db_session()) as db:
                for url in urls:
                    # Parse URL to get domain
                    parsed = urlparse(url)
                    domain = parsed.netloc
                    
                    # Get or create site
                    site = db.query(Site).filter(Site.domain == domain).first()
                    if not site:
                        site = Site(
                            domain=domain,
                            title=f"Site: {domain}",
                            description=f"Discovered site: {domain}"
                        )
                        db.add(site)
                        db.commit()
                        db.refresh(site)
                    
                    # Check if URL already exists
                    existing = db.query(URLFrontier).filter(
                        URLFrontier.url == url
                    ).first()
                    
                    if not existing and depth <= 3:  # Limit depth
                        # Determine priority based on domain
                        priority = 5  # Default priority
                        if site.domain == urlparse(url).netloc:
                            priority = 3  # Higher priority for same domain
                        
                        # Add to frontier
                        url_entry = URLFrontier(
                            url=url,
                            site_id=site.id,
                            priority=priority,
                            depth=depth,
                            source_page_id=source_page_id,
                            status='pending'
                        )
                        db.add(url_entry)
                        added_count += 1
                
                db.commit()
                logger.info(f"Added {added_count} new URLs to frontier")
                
        except Exception as e:
            logger.error(f"Error adding discovered URLs: {e}")
        
        return added_count
    
    def get_next_urls(self, limit: int = 10) -> List[URLFrontier]:
        """Get next URLs to crawl from the frontier"""
        try:
            with next(get_db_session()) as db:
                urls = db.query(URLFrontier).filter(
                    URLFrontier.status == 'pending'
                ).order_by(
                    URLFrontier.priority.asc(),
                    URLFrontier.scheduled_for.asc()
                ).limit(limit).all()
                
                logger.info(f"Retrieved {len(urls)} URLs from frontier")
                return urls
                
        except Exception as e:
            logger.error(f"Error getting next URLs: {e}")
            return []
    
    def mark_url_processing(self, url_id: str) -> bool:
        """Mark a URL as being processed"""
        try:
            with next(get_db_session()) as db:
                url_entry = db.query(URLFrontier).filter(URLFrontier.id == url_id).first()
                if url_entry:
                    url_entry.mark_processing()
                    db.commit()
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Error marking URL as processing: {e}")
            return False
    
    def mark_url_completed(self, url_id: str) -> bool:
        """Mark a URL as completed"""
        try:
            with next(get_db_session()) as db:
                url_entry = db.query(URLFrontier).filter(URLFrontier.id == url_id).first()
                if url_entry:
                    url_entry.mark_completed()
                    db.commit()
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Error marking URL as completed: {e}")
            return False
    
    def get_frontier_stats(self) -> dict:
        """Get statistics about the URL frontier"""
        try:
            with next(get_db_session()) as db:
                stats = {
                    'pending': db.query(URLFrontier).filter(URLFrontier.status == 'pending').count(),
                    'processing': db.query(URLFrontier).filter(URLFrontier.status == 'processing').count(),
                    'completed': db.query(URLFrontier).filter(URLFrontier.status == 'completed').count(),
                    'failed': db.query(URLFrontier).filter(URLFrontier.status == 'failed').count(),
                    'total_sites': db.query(Site).count(),
                    'total_pages': db.query(Page).count()
                }
                return stats
                
        except Exception as e:
            logger.error(f"Error getting frontier stats: {e}")
            return {}

def main():
    """Demo function"""
    discovery = URLDiscoveryEngine()
    
    # Add some seed URLs
    seed_urls = [
        'https://example.com',
        'https://httpbin.org',
        'https://python.org'
    ]
    
    for url in seed_urls:
        discovery.add_seed_url(url)
    
    # Show stats
    stats = discovery.get_frontier_stats()
    print("Frontier Stats:", stats)

if __name__ == "__main__":
    main()
