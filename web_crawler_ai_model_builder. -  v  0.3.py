import os
from bs4 import BeautifulSoup
import requests
import json

dir_path = '/path/to/directory'

Create an empty list to store the URLs
urls = []

Loop through each file in the directory and add each line of the file to the list of URLs
for file in os.listdir(dir_path):
if file.endswith(".txt"):
with open(os.path.join(dir_path, file), 'r') as f:
urls.extend(f.readlines())

Remove any leading or trailing whitespace from the URLs
urls = [url.strip() for url in urls]

Create an empty list to store the scraped data
data = []

Loop through each URL, make a GET request to fetch the raw HTML content, parse the html content, and scrape the desired data
for url in urls:
html_content = requests.get(url).text
soup = BeautifulSoup(html_content, "lxml")
title = soup.find("title").text
body = soup.find("body").text

Copy code
# Store the scraped data in a dictionary and add the dictionary to the list
scraped_data = {
    'title': title,
    'body': body
}
data.append(scraped_data)
Write the list of scraped data to a JSON file
with open('data.json', 'w') as f:
json.dump(data, f)

Loop through each file in the directory again and delete the text file
for file in os.listdir(dir_path):
if file.endswith(".txt"):
os.remove(os.path.join(dir_path, file))
