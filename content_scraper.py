import os
from bs4 import BeautifulSoup
import requests
import json

# Set the directory path where URL files are located
dir_path = './url_files'  # Changed from placeholder path

# Create an empty list to store the URLs
urls = []

# Loop through each file in the directory and add each line of the file to the list of URLs
for file in os.listdir(dir_path):
    if file.endswith(".txt"):
        with open(os.path.join(dir_path, file), 'r') as f:
            urls.extend(f.readlines())

# Remove any leading or trailing whitespace from the URLs
urls = [url.strip() for url in urls]

# Create an empty list to store the scraped data
data = []

# Loop through each URL, make a GET request to fetch the raw HTML content, parse the html content, and scrape the desired data
for url in urls:
    try:
        html_content = requests.get(url).text
        soup = BeautifulSoup(html_content, "lxml")
        title = soup.find("title").text if soup.find("title") else "No title"
        body = soup.find("body").text if soup.find("body") else "No body"

        # Store the scraped data in a dictionary and add the dictionary to the list
        scraped_data = {
            'title': title,
            'body': body,
            'url': url
        }
        data.append(scraped_data)
        print(f"Scraped: {url}")
    except Exception as e:
        print(f"Error scraping {url}: {e}")

# Write the list of scraped data to a JSON file
with open('scraped_data.json', 'w') as f:
    json.dump(data, f, indent=2)

print(f"Scraped {len(data)} URLs and saved to scraped_data.json")

# Loop through each file in the directory again and delete the text files
for file in os.listdir(dir_path):
    if file.endswith(".txt"):
        os.remove(os.path.join(dir_path, file))
        print(f"Deleted: {file}")
