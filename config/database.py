# config/database.py
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
import logging

logger = logging.getLogger(__name__)

class DatabaseConfig:
    """Database configuration management"""
    
    def __init__(self):
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = os.getenv('DB_PORT', '5432')
        self.database = os.getenv('DB_NAME', 'universal_web_directory')
        self.username = os.getenv('DB_USER', 'uwd_app')
        self.password = os.getenv('DB_PASSWORD', 'secure_password_here')

    @property
    def connection_string(self):
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

    def validate_connection(self):
        """Validate database connection parameters"""
        if not self.password or self.password == 'secure_password_here':
            logger.warning("Using default password. Please set DB_PASSWORD environment variable.")
        
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'username': self.username
        }

# Database session management
def create_database_engine():
    """Create SQLAlchemy engine with proper configuration"""
    config = DatabaseConfig()
    
    engine = create_engine(
        config.connection_string,
        pool_size=20,
        max_overflow=30,
        pool_pre_ping=True,
        echo=os.getenv('DB_ECHO', 'false').lower() == 'true'
    )
    
    return engine

# Global database objects
engine = None
SessionLocal = None
Base = declarative_base()

def initialize_database():
    """Initialize database engine and session factory"""
    global engine, SessionLocal
    
    engine = create_database_engine()
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    logger.info("Database initialized successfully")
    return engine, SessionLocal

def get_db_session():
    """Get database session with proper cleanup"""
    if SessionLocal is None:
        initialize_database()
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
