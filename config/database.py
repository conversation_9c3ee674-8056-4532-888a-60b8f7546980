# config/database.py
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import declarative_base
import logging

logger = logging.getLogger(__name__)

class DatabaseConfig:
    """Database configuration management"""
    
    def __init__(self):
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = os.getenv('DB_PORT', '5432')
        self.database = os.getenv('DB_NAME', 'web_crawler')
        self.username = os.getenv('DB_USER', 'webc')
        self.password = os.getenv('DB_PASSWORD', 'j9xuvUyTHBKEld4JeP9FO')

    @property
    def connection_string(self):
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

    def validate_connection(self):
        """Validate database connection parameters"""
        if not self.password or self.password == 'secure_password_here':
            logger.warning("Using default password. Please set DB_PASSWORD environment variable.")
        
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'username': self.username
        }

# Database session management
def create_database_engine():
    """Create SQLAlchemy engine with proper configuration"""
    config = DatabaseConfig()
    
    engine = create_engine(
        config.connection_string,
        pool_size=20,
        max_overflow=30,
        pool_pre_ping=True,
        echo=os.getenv('DB_ECHO', 'false').lower() == 'true'
    )
    
    return engine

# Global database objects
Base = declarative_base()
engine = create_database_engine()
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Call initialize_database on import to log and potentially handle other setup tasks if any were there.
# Or, ensure create_database_engine and sessionmaker are sufficient for module-level init.
# For simplicity and alignment with roadmap, direct initialization is used.
logger.info("Database engine and SessionLocal initialized at module level.")


def get_db_session():
    """Get database session with proper cleanup"""
    # SessionLocal is now initialized at module level.
    # The None check below is a safeguard; it should ideally not be hit.
    if SessionLocal is None:
        # This indicates a problem with the module-level initialization.
        # Raising an error might be more appropriate than trying to re-initialize here.
        logger.critical("FATAL: SessionLocal is None in get_db_session even after module-level initialization.")
        raise RuntimeError("Database session factory (SessionLocal) not initialized.")

    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
