# api/main.py
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Security, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging
from datetime import datetime, timedelta
import asyncio # Added for background tasks
from fastapi import BackgroundTasks # Added for background tasks

# Import monitoring services
from services.monitoring import metrics_collector, performance_monitor

# Initialize FastAPI with comprehensive metadata
app = FastAPI(
    title="Universal Web Directory API",
    description="""
    A comprehensive web intelligence API providing access to crawled web data,
    semantic search capabilities, and content analysis insights.

    ## Features
    * **Search**: Semantic and keyword-based search across millions of web pages
    * **Content Analysis**: NLP-powered content classification and entity extraction
    * **Site Intelligence**: Domain-level insights and authority scoring
    * **Real-time Data**: Fresh content with automated crawling and updates
    """,
    version="1.0.0",
    contact={
        "name": "Universal Web Directory Team",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    docs_url="/docs",
    redoc_url="/redoc",
)

# Security and middleware
security = HTTPBearer()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://universalwebdirectory.com", "https://api.universalwebdirectory.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["universalwebdirectory.com", "*.universalwebdirectory.com", "localhost"],
)


# Request/Response Models
class SearchRequest(BaseModel):
    """Search request model with validation"""

    query: str = Field(..., min_length=1, max_length=500, description="Search query")
    search_type: str = Field(
        "hybrid", pattern="^(semantic|keyword|hybrid)$", description="Type of search"
    )
    limit: int = Field(10, ge=1, le=100, description="Number of results to return")
    offset: int = Field(0, ge=0, description="Pagination offset")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional search filters")

    class Config:
        schema_extra = {
            "example": {
                "query": "artificial intelligence machine learning",
                "search_type": "hybrid",
                "limit": 20,
                "offset": 0,
                "filters": {
                    "language": "en",
                    "domain_authority": {"min": 50},
                    "content_type": ["article", "blog"],
                },
            }
        }


class SearchResult(BaseModel):
    """Individual search result model"""

    page_id: str
    url: str
    title: str
    description: Optional[str]
    similarity_score: Optional[float] = Field(None, ge=0, le=1)
    domain: str
    content_type: Optional[str]
    language: Optional[str]
    last_crawled: datetime
    authority_score: Optional[float]


class SearchResponse(BaseModel):
    """Search response model"""

    query: str
    search_type: str
    total_results: int
    results: List[SearchResult]
    response_time_ms: int
    pagination: Dict[str, Any]


class SiteAnalysis(BaseModel):
    """Site analysis response model"""

    domain: str
    title: Optional[str]
    description: Optional[str]
    authority_score: Optional[float]
    page_count: int
    last_crawled: Optional[datetime]
    technology_stack: Optional[Dict[str, Any]]
    content_categories: List[str]
    language_distribution: Dict[str, int]


# Authentication dependency
async def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)):
    """Verify API key authentication"""
    from services.auth.api_auth import APIAuthService

    auth_service = APIAuthService()
    api_key = credentials.credentials

    if not await auth_service.verify_api_key(api_key):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return api_key


# Background task for performance monitoring
async def run_performance_checks():
    while True:
        try:
            logging.info("Running performance alert checks...")
            await performance_monitor.check_performance_alerts()
        except Exception as e:
            logging.error(f"Error in performance_monitor.check_performance_alerts: {e}")
        await asyncio.sleep(300) # Check every 5 minutes

@app.on_event("startup")
async def startup_event():
    # Start the background task
    asyncio.create_task(run_performance_checks())
    logging.info("Performance monitoring background task started.")

# API Routes
@app.get("/", tags=["Health"])
async def root():
    """API health check and basic information"""
    metrics_collector.increment_counter("api.requests.total", tags={"endpoint": "/", "method": "GET"})
    start_time_metric = datetime.now()

    response = {
        "service": "Universal Web Directory API",
        "version": "1.0.0",
        "status": "operational",
        "timestamp": datetime.utcnow().isoformat(),
        "documentation": "/docs",
    }

    duration_ms = (datetime.now() - start_time_metric).total_seconds() * 1000
    metrics_collector.record_timer("api.response_time_ms", duration_ms, tags={"endpoint": "/", "method": "GET", "status_code": 200})
    return response


@app.post("/search", response_model=SearchResponse, tags=["Search"])
async def search_content(request: SearchRequest, api_key: str = Depends(verify_api_key)):
    """
    Perform semantic or keyword search across the web directory

    - **query**: Search terms or natural language query
    - **search_type**: Choose between semantic, keyword, or hybrid search
    - **limit**: Number of results (1-100)
    - **offset**: Pagination offset
    - **filters**: Additional filters for domain, language, content type, etc.
    """
    metrics_collector.increment_counter("api.requests.total", tags={"endpoint": "/search", "method": "POST"})
    start_time_metric = datetime.now()
    status_code = 200 # Default success

    from services.search.search_service import SearchService

    start_time = datetime.now()

    try:
        search_service = SearchService()
        results = await search_service.search(
            query=request.query,
            search_type=request.search_type,
            limit=request.limit,
            offset=request.offset,
            filters=request.filters,
        )

        end_time = datetime.now()
        response_time_calc = int((end_time - start_time).total_seconds() * 1000)

        response = SearchResponse(
            query=request.query,
            search_type=request.search_type,
            total_results=results["total_count"],
            results=[SearchResult(**result) for result in results["items"]],
            response_time_ms=response_time_calc,
            pagination={
                "limit": request.limit,
                "offset": request.offset,
                "has_next": results["has_next"],
                "total_pages": results["total_pages"],
            },
        )
        return response

    except HTTPException as http_exc: # Capture HTTPException to record status_code
        status_code = http_exc.status_code
        metrics_collector.increment_counter("api.errors.total", tags={"endpoint": "/search", "method": "POST", "status_code": status_code})
        raise http_exc
    except Exception as e:
        status_code = 500
        metrics_collector.increment_counter("api.errors.total", tags={"endpoint": "/search", "method": "POST", "status_code": status_code})
        logging.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail="Search service temporarily unavailable")
    finally:
        duration_ms = (datetime.now() - start_time_metric).total_seconds() * 1000
        metrics_collector.record_timer("api.response_time_ms", duration_ms, tags={"endpoint": "/search", "method": "POST", "status_code": status_code})


@app.get(
    "/sites/{domain}/analysis",
    response_model=SiteAnalysis,
    tags=["Site Intelligence"],
)
async def get_site_analysis(domain: str, api_key: str = Depends(verify_api_key)):
    """
    Get comprehensive analysis for a specific domain

    Returns domain-level insights including:
    - Authority and trust metrics
    - Content categorization
    - Technology stack detection
    - Language distribution
    - Crawling statistics
    """
    metrics_collector.increment_counter("api.requests.total", tags={"endpoint": "/sites/{domain}/analysis", "method": "GET"})
    start_time_metric = datetime.now()
    status_code = 200

    from services.analysis.site_analyzer import SiteAnalyzer

    try:
        analyzer = SiteAnalyzer()
        analysis = await analyzer.analyze_domain(domain)

        if not analysis:
            status_code = 404
            raise HTTPException(status_code=404, detail="Domain not found in directory")

        response = SiteAnalysis(**analysis)
        return response

    except HTTPException as http_exc:
        status_code = http_exc.status_code
        metrics_collector.increment_counter("api.errors.total", tags={"endpoint": "/sites/{domain}/analysis", "method": "GET", "status_code": status_code})
        raise http_exc
    except Exception as e:
        status_code = 500
        metrics_collector.increment_counter("api.errors.total", tags={"endpoint": "/sites/{domain}/analysis", "method": "GET", "status_code": status_code})
        logging.error(f"Site analysis failed for {domain}: {e}")
        raise HTTPException(status_code=500, detail="Analysis service temporarily unavailable")
    finally:
        duration_ms = (datetime.now() - start_time_metric).total_seconds() * 1000
        metrics_collector.record_timer("api.response_time_ms", duration_ms, tags={"endpoint": "/sites/{domain}/analysis", "method": "GET", "status_code": status_code})


@app.get("/content/{page_id}", tags=["Content"])
async def get_page_content(
    page_id: str,
    include_analysis: bool = False,
    api_key: str = Depends(verify_api_key),
):
    """
    Retrieve detailed content and metadata for a specific page

    - **page_id**: Unique identifier for the page
    - **include_analysis**: Include NLP analysis results (entities, topics, sentiment)
    """
    metrics_collector.increment_counter("api.requests.total", tags={"endpoint": "/content/{page_id}", "method": "GET"})
    start_time_metric = datetime.now()
    status_code = 200

    from services.content.content_service import ContentService

    try:
        content_service = ContentService()
        content = await content_service.get_page_details(page_id, include_analysis)

        if not content:
            status_code = 404
            raise HTTPException(status_code=404, detail="Page not found")

        return content

    except HTTPException as http_exc:
        status_code = http_exc.status_code
        metrics_collector.increment_counter("api.errors.total", tags={"endpoint": "/content/{page_id}", "method": "GET", "status_code": status_code})
        raise http_exc
    except Exception as e:
        status_code = 500
        metrics_collector.increment_counter("api.errors.total", tags={"endpoint": "/content/{page_id}", "method": "GET", "status_code": status_code})
        logging.error(f"Content retrieval failed for page {page_id}: {e}")
        raise HTTPException(status_code=500, detail="Content service temporarily unavailable")
    finally:
        duration_ms = (datetime.now() - start_time_metric).total_seconds() * 1000
        metrics_collector.record_timer("api.response_time_ms", duration_ms, tags={"endpoint": "/content/{page_id}", "method": "GET", "status_code": status_code})


@app.get("/trending", tags=["Intelligence"])
async def get_trending_topics(
    timeframe: str = "24h",
    limit: int = 20,
    api_key: str = Depends(verify_api_key),
):
    """
    Get trending topics and emerging content patterns

    - **timeframe**: Time period for trend analysis (1h, 24h, 7d, 30d)
    - **limit**: Number of trending topics to return
    """
    metrics_collector.increment_counter("api.requests.total", tags={"endpoint": "/trending", "method": "GET"})
    start_time_metric = datetime.now()
    status_code = 200

    from services.intelligence.trend_analyzer import TrendAnalyzer

    try:
        trend_analyzer = TrendAnalyzer()
        trends = await trend_analyzer.get_trending_topics(timeframe, limit)

        response = {
            "timeframe": timeframe,
            "trending_topics": trends,
            "generated_at": datetime.utcnow().isoformat(),
        }
        return response

    except HTTPException as http_exc:
        status_code = http_exc.status_code
        metrics_collector.increment_counter("api.errors.total", tags={"endpoint": "/trending", "method": "GET", "status_code": status_code})
        raise http_exc
    except Exception as e:
        status_code = 500
        metrics_collector.increment_counter("api.errors.total", tags={"endpoint": "/trending", "method": "GET", "status_code": status_code})
        logging.error(f"Trend analysis failed: {e}")
        raise HTTPException(
            status_code=500, detail="Trend analysis service temporarily unavailable"
        )
    finally:
        duration_ms = (datetime.now() - start_time_metric).total_seconds() * 1000
        metrics_collector.record_timer("api.response_time_ms", duration_ms, tags={"endpoint": "/trending", "method": "GET", "status_code": status_code})


# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    # Log error metric for HTTPExceptions handled here if not already logged by endpoint
    # This is a general handler, specific endpoint might have already logged.
    # For simplicity, we assume endpoints log their own handled HTTPExceptions.
    # If an HTTPException bubbles up to here without prior logging, it could be logged.
    # Example: metrics_collector.increment_counter("api.errors.unhandled_http", tags={"status_code": exc.status_code})
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.utcnow().isoformat(),
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    # Log error metric for unhandled exceptions
    metrics_collector.increment_counter("api.errors.unhandled_server_error", tags={"exception_type": type(exc).__name__})
    logging.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "status_code": 500,
            "timestamp": datetime.utcnow().isoformat(),
        },
    )


if __name__ == "__main__":
    # Configure logging for the main application if run directly
    # This is good for local dev, but in prod, logging might be configured differently (e.g., by Uvicorn args or logging config file)
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True, # Reload should be False in production
        log_level="info",
    )
